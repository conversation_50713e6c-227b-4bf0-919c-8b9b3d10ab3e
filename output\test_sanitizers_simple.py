#!/usr/bin/env python3
"""
Simple test script to verify the sanitizers helper methods in expire.py

This script tests the new helper methods without requiring the full elipy2 environment.
"""

import os
import sys

# Add the elipy2 module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'pycharm', 'elipy2'))


def test_is_main_sanitizer_directory():
    """Test the _is_main_sanitizer_directory logic"""
    print("Testing _is_main_sanitizer_directory logic...")
    
    def is_main_sanitizer_directory(path_parts):
        """Simplified version of the helper method"""
        if not path_parts:
            return False
        
        last_part = path_parts[-1].lower()
        # Check if the last part ends with 'sanitizers' but is not a sanitizer type subdirectory
        return (
            last_part.endswith("sanitizers") and 
            last_part not in {"asan", "ubsan", "msan", "tsan", "lsan"}
        )
    
    test_cases = [
        (["code", "ch1-content-dev-sanitizers"], True),
        (["code", "ch1-code-dev-sanitizers"], True),
        (["code", "trunk-code-dev-sanitizers"], True),
        (["code", "ch1-content-dev-sanitizers", "asan"], False),
        (["code", "ch1-content-dev-sanitizers", "ubsan"], False),
        (["code", "regular-directory"], False),
        ([], False),
    ]
    
    all_passed = True
    for path_parts, expected in test_cases:
        result = is_main_sanitizer_directory(path_parts)
        if result == expected:
            print(f"✓ PASS: is_main_sanitizer_directory({path_parts}) = {result}")
        else:
            print(f"✗ FAIL: is_main_sanitizer_directory({path_parts}) = {result}, expected {expected}")
            all_passed = False
    
    return all_passed


def test_is_sanitizer_subdirectory():
    """Test the _is_sanitizer_subdirectory logic"""
    print("\nTesting _is_sanitizer_subdirectory logic...")
    
    def is_sanitizer_subdirectory(path_parts):
        """Simplified version of the helper method"""
        if len(path_parts) < 2:
            return False
        
        last_part = path_parts[-1].lower()
        parent_part = path_parts[-2].lower()
        
        # Check if parent ends with 'sanitizers' and last part is a known sanitizer type
        return (
            parent_part.endswith("sanitizers") and 
            last_part in {"asan", "ubsan", "msan", "tsan", "lsan"}
        )
    
    test_cases = [
        (["code", "ch1-content-dev-sanitizers", "asan"], True),
        (["code", "ch1-content-dev-sanitizers", "ubsan"], True),
        (["code", "trunk-code-dev-sanitizers", "asan"], True),
        (["code", "ch1-content-dev-sanitizers"], False),
        (["code", "ch1-code-dev-sanitizers"], False),
        (["code", "regular-directory", "asan"], False),
        (["asan"], False),
        ([], False),
    ]
    
    all_passed = True
    for path_parts, expected in test_cases:
        result = is_sanitizer_subdirectory(path_parts)
        if result == expected:
            print(f"✓ PASS: is_sanitizer_subdirectory({path_parts}) = {result}")
        else:
            print(f"✗ FAIL: is_sanitizer_subdirectory({path_parts}) = {result}, expected {expected}")
            all_passed = False
    
    return all_passed


def test_path_logic():
    """Test the path logic for real-world examples"""
    print("\nTesting real-world path examples...")
    
    def is_main_sanitizer_directory(path_parts):
        if not path_parts:
            return False
        last_part = path_parts[-1].lower()
        return (
            last_part.endswith("sanitizers") and 
            last_part not in {"asan", "ubsan", "msan", "tsan", "lsan"}
        )
    
    def is_sanitizer_subdirectory(path_parts):
        if len(path_parts) < 2:
            return False
        last_part = path_parts[-1].lower()
        parent_part = path_parts[-2].lower()
        return (
            parent_part.endswith("sanitizers") and 
            last_part in {"asan", "ubsan", "msan", "tsan", "lsan"}
        )
    
    # Real paths from the Jenkins log
    real_paths = [
        "\\\\filer.dice.ad.ea.com\\builds\\Battlefield\\code\\ch1-content-dev-sanitizers",
        "\\\\filer.dice.ad.ea.com\\builds\\Battlefield\\code\\ch1-content-dev-sanitizers\\asan",
        "\\\\filer.dice.ad.ea.com\\builds\\Battlefield\\code\\ch1-content-dev-sanitizers\\ubsan",
        "\\\\filer.dice.ad.ea.com\\builds\\Battlefield\\code\\ch1-code-dev-sanitizers\\asan",
        "\\\\filer.dice.ad.ea.com\\builds\\Battlefield\\code\\ch1-code-dev-sanitizers\\ubsan",
    ]
    
    expected_results = [
        ("ch1-content-dev-sanitizers", True, False),  # Main sanitizer dir
        ("ch1-content-dev-sanitizers\\asan", False, True),  # Sanitizer subdir
        ("ch1-content-dev-sanitizers\\ubsan", False, True),  # Sanitizer subdir
        ("ch1-code-dev-sanitizers\\asan", False, True),  # Sanitizer subdir
        ("ch1-code-dev-sanitizers\\ubsan", False, True),  # Sanitizer subdir
    ]
    
    all_passed = True
    for i, path in enumerate(real_paths):
        path_parts = os.path.normpath(path).split(os.sep)
        is_main = is_main_sanitizer_directory(path_parts)
        is_sub = is_sanitizer_subdirectory(path_parts)
        
        expected_name, expected_main, expected_sub = expected_results[i]
        
        if is_main == expected_main and is_sub == expected_sub:
            print(f"✓ PASS: {expected_name} -> main={is_main}, sub={is_sub}")
        else:
            print(f"✗ FAIL: {expected_name} -> main={is_main}, sub={is_sub}, expected main={expected_main}, sub={expected_sub}")
            all_passed = False
    
    return all_passed


def main():
    """Run all tests"""
    print("Testing sanitizers helper methods")
    print("=" * 50)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_is_main_sanitizer_directory()
    all_passed &= test_is_sanitizer_subdirectory()
    all_passed &= test_path_logic()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ ALL TESTS PASSED")
        print("\nThe sanitizers fix should work correctly:")
        print("1. Main sanitizer directories will only include direct CL directories")
        print("2. Sanitizer subdirectories (asan, ubsan) will be handled separately")
        print("3. This matches the user's requirements for the three branches:")
        print("   - ch1-content-dev-sanitizers (direct CLs only)")
        print("   - ch1-content-dev-sanitizers\\asan (asan CLs only)")
        print("   - ch1-content-dev-sanitizers\\ubsan (ubsan CLs only)")
        return 0
    else:
        print("✗ SOME TESTS FAILED")
        return 1


if __name__ == "__main__":
    sys.exit(main())
