"""
test_data.py
"""

import os
import pytest
import time
from mock import call, MagicMock, mock_open, patch
import elipy2
from elipy2 import frostbite_core, SETTINGS
from elipy2.data import DataUtils
from elipy2.exceptions import ELIPYException


class TestData(object):
    def setup(self):
        from elipy2.data import DataUtils

        self.mock_avalanche___http_get = patch("elipy2.avalanche._http_get")
        self.mock_avalanche___http_get.start()

        self.patcher_set_datadir = patch("elipy2.frostbite.fbenv_layer.fbenv.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_set_datadir_fbcli = patch("elipy2.frostbite.fbenv_layer.fbcli.set_datadir")
        self.mock_set_datadir_fbcli = self.patcher_set_datadir_fbcli.start()

        self.patcher_ensure_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_config = self.patcher_ensure_config.start()

        self.patcher_core_run = patch("elipy2.core.run")
        self.mock_core_run = self.patcher_core_run.start()

        self.builder = DataUtils("win64", ["retaillevels"])
        os.environ["TEMP"] = "/random/path"

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        self.patcher_fbenv_layer_cook = patch("elipy2.frostbite.fbenv_layer.cook")
        self.mock_fbenv_layer_cook = self.patcher_fbenv_layer_cook.start()

    def teardown(self):
        patch.stopall()

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_fbenv(self):
        self.builder.cook()
        calls = [
            call(index=True, platforms=["index"], is_local=True, pipeline_args=[]),
            call(
                platforms=["win64"],
                assets=["retaillevels"],
                pipeline_args=["-trim"],
                is_local=True,
            ),
        ]
        self.mock_fbenv_layer_cook.assert_has_calls(calls, any_order=True)

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_fbcli(self):
        self.builder.cook()
        calls = [
            call(index=True, platforms=["index"], is_local=True, pipeline_args=[]),
            call(
                platforms=["win64"],
                assets=["retaillevels"],
                pipeline_args=["-trim"],
                is_local=True,
            ),
        ]
        self.mock_fbenv_layer_cook.assert_has_calls(calls, any_order=True)

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_fbcli_skip_indexing(self):
        self.builder.cook(skip_indexing=True)
        cook = call(
            platforms=["win64"],
            assets=["retaillevels"],
            pipeline_args=["-trim"],
            is_local=True,
        )
        assert self.mock_fbenv_layer_cook.call_count == 1
        assert self.mock_fbenv_layer_cook.call_args_list[0] == cook

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_fbcli_only_indexing(self):
        self.builder.cook(only_indexing=True)
        index = call(index=True, platforms=["index"], is_local=True, pipeline_args=[])
        assert self.mock_fbenv_layer_cook.call_count == 1
        assert self.mock_fbenv_layer_cook.call_args_list[0] == index

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_asset_clean_master(self):
        clean_master_version_args = [
            "-Pipeline.EnableDatabaseMasterVersion",
            "true",
            "-Pipeline.AllowSourceDataModulePathOverrides",
            "false",
        ]
        self.builder.cook(
            assets=["Levels"], trim=False, indexing_args=["-test"], clean_master_version_check=True
        )
        calls = [
            call(
                index=True,
                platforms=["index"],
                is_local=True,
                pipeline_args=clean_master_version_args + ["-test"],
            ),
            call(
                platforms=["win64"],
                assets=["Levels"],
                pipeline_args=clean_master_version_args,
                is_local=True,
            ),
        ]
        self.mock_fbenv_layer_cook.assert_has_calls(calls, any_order=True)

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_with_disable_caches(self):
        disable_caches_args = [
            "-clean",
            "-forget",
            "-Cache.Disable",
            "true",
            "-forceBuild",
            "-trace",
        ]

        self.builder.cook(
            assets=["Levels"],
            trim=False,
            clean_master_version_check=False,
            disable_caches=True,
        )
        calls = [
            call(index=True, platforms=["index"], is_local=True, pipeline_args=disable_caches_args),
            call(
                platforms=["win64"],
                assets=["Levels"],
                pipeline_args=disable_caches_args,
                is_local=True,
            ),
        ]
        self.mock_fbenv_layer_cook.assert_has_calls(calls, any_order=True)

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_cook_clean_index(self):
        clean_index_args = ["-clean"]
        self.builder.cook(
            assets=["Levels"],
            trim=False,
            clean_index=True,
            clean_master_version_check=False,
            disable_caches=False,
        )
        calls = [
            call(index=True, platforms=["index"], is_local=True, pipeline_args=clean_index_args),
            call(
                platforms=["win64"],
                assets=["Levels"],
                pipeline_args=[],
                is_local=True,
            ),
        ]
        self.mock_fbenv_layer_cook.assert_has_calls(calls, any_order=True)

    def test_cook_with_exception(self):
        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(Exception):
            self.builder.cook()

    def test_cook_with_exception_asset(self):
        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.builder.cook(assets=12)

    def test_cook_with_exception_source_layer(self, mocker):
        mock_logger = mocker.patch("logging.Logger.error", return_value=None)
        self.mock_fbenv_layer_cook.side_effect = Exception()
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
        ]
        with pytest.raises(Exception):
            self.builder.cook(assets=test_assets)
        msg = mock_logger.mock_calls[0][1][0]
        params = mock_logger.mock_calls[0][1][1:]
        assert msg % tuple(params) == f"Failed to cook data for win64 on source layer {test_assets}"

    def test_cook_with_exception_content_layer(self, mocker):
        layer = "C3B2S5"
        mock_logger = mocker.patch("logging.Logger.error", return_value=None)
        self.mock_fbenv_layer_cook.side_effect = Exception()
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
        ]
        with pytest.raises(Exception):
            self.builder.cook(assets=test_assets, pipeline_args=["-activeContentLayer", layer])
        msg = mock_logger.mock_calls[0][1][0]
        params = mock_logger.mock_calls[0][1][1:]
        assert (
            msg % tuple(params)
            == f"Failed to cook data for win64 on content layer '{layer}' {test_assets}"
        )

    @patch("elipy2.data.DataUtils.copy_dmp_and_mdmp_to_filer")
    # Check for minidumps when exception happens.
    def test_cook_with_exception_and_flag_calls_copy_dmp_and_mdmp_to_filer(
        self, mock_copy_dmp_and_mdmp_to_filer
    ):
        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(Exception):
            self.builder.cook(collect_mdmps=True)
        assert mock_copy_dmp_and_mdmp_to_filer.call_count == 1

    @patch("elipy2.data.DataUtils.copy_dmp_and_mdmp_to_filer")
    def test_cook_with_exception_without_flag_doesnt_call_copy_dmp_and_mdmp_to_filer(
        self, mock_copy_dmp_and_mdmp_to_filer
    ):
        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(Exception):
            self.builder.cook()
        assert mock_copy_dmp_and_mdmp_to_filer.call_count == 0

    @patch("elipy2.data.node")
    @patch("os.makedirs", MagicMock())
    @patch("os.listdir")
    @patch("os.path.isfile")
    @patch("shutil.move")
    # Called N times when N minidumps are called
    def test_copy_dmp_and_mdmp_to_filer(
        self,
        mock_shutil_move,
        mock_os_path_isfile,
        mock_os_listdir,
        mock_node,
    ):
        mock_os_listdir.return_value = ["foo.mdmp", "bar.mdmp"]
        mock_os_path_isfile.return_value = True
        mock_node.return_value = "test_node"
        self.builder.copy_dmp_and_mdmp_to_filer()
        calls = [
            call(
                os.path.join(os.environ.get("TEMP"), "foo.mdmp"),
                os.path.join(
                    SETTINGS.get("build_share"),
                    "crashdumps",
                    "pipeline_crashdumps",
                    "test_node.foo.mdmp",
                ),
            ),
            call(
                os.path.join(os.environ.get("TEMP"), "bar.mdmp"),
                os.path.join(
                    SETTINGS.get("build_share"),
                    "crashdumps",
                    "pipeline_crashdumps",
                    "test_node.bar.mdmp",
                ),
            ),
        ]
        mock_shutil_move.assert_has_calls(calls)

    @patch("elipy2.data.node", MagicMock())
    @patch("os.makedirs")
    @patch("os.listdir")
    @patch("os.path.isfile")
    @patch("os.path.exists")
    @patch("shutil.move", MagicMock())
    def test_copy_dmp_and_mdmp_to_filer_create_dir_if_not_existing(
        self,
        mock_os_path_exist,
        mock_os_path_isfile,
        mock_os_listdir,
        mock_os_makedirs,
    ):
        mock_os_listdir.return_value = ["foo.mdmp", "bar.mdmp"]
        mock_os_path_isfile.return_value = True
        mock_os_path_exist.return_value = False
        self.builder.copy_dmp_and_mdmp_to_filer()
        assert mock_os_makedirs.call_count == 2

    @patch("elipy2.data.node", MagicMock())
    @patch("os.makedirs")
    @patch("os.listdir")
    @patch("os.path.isfile")
    @patch("os.path.exists")
    @patch("shutil.move", MagicMock())
    def test_copy_dmp_and_mdmp_to_filer_dont_create_dir_if_already_existing(
        self,
        mock_os_path_exist,
        mock_os_path_isfile,
        mock_os_listdir,
        mock_os_makedirs,
    ):
        mock_os_listdir.return_value = ["foo.mdmp", "bar.mdmp"]
        mock_os_path_isfile.return_value = True
        mock_os_path_exist.return_value = True
        self.builder.copy_dmp_and_mdmp_to_filer()
        assert mock_os_makedirs.call_count == 0

    @patch("os.makedirs", MagicMock())
    @patch("os.listdir")
    @patch("os.path.isfile")
    @patch("shutil.move")
    # No calls if no minidumps are found
    def test_copy_dmp_and_mdmp_to_filer_no_mdmp(
        self, mock_shutil_move, mock_os_path_isfile, mock_os_listdir
    ):
        mock_os_listdir.return_value = []
        mock_os_path_isfile.return_value = True
        self.builder.copy_dmp_and_mdmp_to_filer()
        assert mock_shutil_move.call_count == 0

    @patch("os.makedirs", MagicMock())
    @patch("os.listdir")
    @patch("os.path.isfile")
    @patch("shutil.move")
    # No calls if no minidumps are found
    def test_copy_dmp_and_mdmp_to_filer_only_move_mdmp_files(
        self, mock_shutil_move, mock_os_path_isfile, mock_os_listdir
    ):
        mock_os_listdir.return_value = ["foo.mdmp", "not_a_dump.txt"]
        mock_os_path_isfile.return_value = True
        self.builder.copy_dmp_and_mdmp_to_filer()
        assert mock_shutil_move.call_count == 1

    @patch("elipy2.avalanche.get_database_id")
    def test_set_datadir(self, mock_get_database_id):
        mock_get_database_id.return_value = os.path.join(
            elipy2.frostbite_core.get_game_root(), "wsdata"
        )
        self.builder.set_datadir("wsdata")
        self.mock_set_datadir.assert_called_once_with(
            os.path.join(elipy2.frostbite_core.get_game_root(), "wsdata"), store_value=True
        )

    @patch("elipy2.core.ensure_p4_config")
    @patch("elipy2.frostbite_core.set_monkey_build_label")
    @patch("elipy2.core.is_buildsystem_run")
    def test_clean_fbenv(self, mock_autobuild, mock_set_monkey, mock_p4config):
        mock_autobuild.return_value = True
        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        builder.clean()
        self.mock_fbenv_layer_cook.assert_called_once_with(
            "win64",
            assets=[],
            attach=False,
            pipeline_args=["-clean", "-updateIndex"],
            is_local=True,
        )
        mock_set_monkey.assert_called_once_with(None)
        mock_p4config.assert_called_once_with()

    def test_clean_reimport_without_platform_arg(self):
        builder = elipy2.data.DataUtils("reimport", ["retaillevels"])
        builder.clean()
        self.mock_fbenv_layer_cook.assert_called_once_with(
            "win64",
            assets=[],
            attach=False,
            pipeline_args=["-clean", "-updateIndex"],
            is_local=True,
        )

    def test_clean_reimport_with_platform_arg(self):
        builder = elipy2.data.DataUtils("reimport", ["retaillevels"])
        builder.clean("ps5")
        self.mock_fbenv_layer_cook.assert_called_once_with(
            "ps5",
            assets=[],
            attach=False,
            pipeline_args=["-clean", "-updateIndex"],
            is_local=True,
        )

    @patch("elipy2.core.ensure_p4_config")
    @patch("elipy2.frostbite_core.set_monkey_build_label")
    @patch("elipy2.core.is_buildsystem_run")
    def test_clean_extra_pipeline_args(self, mock_autobuild, mock_set_monkey, mock_p4config):
        mock_autobuild.return_value = True
        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        builder.clean(extra_pipeline_args=["some_arg"])
        self.mock_fbenv_layer_cook.assert_called_once_with(
            "win64",
            assets=[],
            attach=False,
            pipeline_args=["-clean", "-updateIndex", "some_arg"],
            is_local=True,
        )
        mock_set_monkey.assert_called_once_with(None)
        mock_p4config.assert_called_once_with()

    @patch("elipy2.data.open", new_callable=mock_open, read_data="filename file")
    @patch("os.makedirs", MagicMock())
    @patch("elipy2.core.delete_folder")
    def test_clean_ant_local(self, mock_core_delete_folder, mock_open_file):
        self.builder.clean_ant_local()
        mock_core_delete_folder.assert_called_once_with(
            os.path.join(elipy2.local_paths.get_ant_local_dir(), "Local")
        )
        mock_open_file.assert_called_once_with(
            os.path.join(elipy2.local_paths.get_ant_local_dir(), "Animations.apj"), "x"
        )

    @patch("os.makedirs", MagicMock())
    @patch("os.path.exists")
    @patch("elipy2.data.open", new_callable=mock_open, read_data="filename file")
    @patch("elipy2.core.delete_folder")
    def test_clean_ant_local_no_file(self, mock_core_delete_folder, _, mock_exists):
        mock_exists.return_value = False
        self.builder.clean_ant_local()
        mock_core_delete_folder.assert_called_once_with(
            os.path.join(elipy2.local_paths.get_ant_local_dir(), "Local")
        )

    @patch("elipy2.data.open", new_callable=mock_open, read_data="filename file")
    @patch("os.makedirs")
    @patch("elipy2.core.delete_folder")
    @patch("os.path.exists")
    def test_clean_ant_local_dont_recreate_file_if_existing(
        self, mock_os_path_exists, mock_core_delete_folder, mock_os_makedirs, mock_open_file
    ):
        mock_os_path_exists.return_value = True
        self.builder.clean_ant_local()
        mock_core_delete_folder.assert_called_once_with(
            os.path.join(elipy2.local_paths.get_ant_local_dir(), "Local")
        )
        assert mock_os_makedirs.call_count == 0
        assert mock_open_file.call_count == 0

    @patch("elipy2.core.delete_folder")
    def test_clean_local_datastate(self, mock_core_delete_folder):
        self.builder.clean_local_datastate()
        state_local = os.path.join(elipy2.frostbite_core.get_game_data_dir(), ".state")
        mock_core_delete_folder.assert_called_once_with(state_local)

    @patch("elipy2.local_paths.get_local_expressiondebug_path")
    @patch("elipy2.core.delete_folder", MagicMock())
    def test_extract_expression_debugdata(self, mock_local_path):
        mock_local_path.return_value = "local_path"
        self.builder.extract_expression_debugdata()
        expression_debug_data_args = [
            "-f",
            "extractExpressionDebugData",
            "-exportDir",
            "local_path",
        ]
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"],
            assets=["retaillevels"],
            pipeline_args=expression_debug_data_args,
            is_local=True,
        )

    @patch("elipy2.local_paths.get_local_expressiondebug_path")
    @patch("elipy2.core.delete_folder", MagicMock())
    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_extract_expression_debugdata_with_clean_args(self, mock_local_path):
        mock_local_path.return_value = "local_path"
        self.builder.extract_expression_debugdata(clean_master_version_check=True)
        expression_debug_data_args = [
            "-f",
            "extractExpressionDebugData",
            "-exportDir",
            "local_path",
        ]
        clean_master_version_args = [
            "-Pipeline.EnableDatabaseMasterVersion",
            "true",
            "-Pipeline.AllowSourceDataModulePathOverrides",
            "false",
        ]
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"],
            assets=["retaillevels"],
            pipeline_args=expression_debug_data_args + clean_master_version_args,
            is_local=True,
        )

    @patch("elipy2.SETTINGS.get")
    @patch("elipy2.local_paths.get_local_expressiondebug_path", MagicMock())
    @patch("elipy2.core.delete_folder", MagicMock())
    def test_extract_expression_debugdata_fail_not_bct(self, mock_get_settings):
        mock_get_settings.return_value = "not_bct"
        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(Exception):
            self.builder.extract_expression_debugdata()

    @patch("elipy2.SETTINGS.get")
    @patch("elipy2.local_paths.get_local_expressiondebug_path", MagicMock())
    @patch("elipy2.core.delete_folder", MagicMock())
    def test_extract_expression_debugdata_fail_bct(self, mock_get_settings):
        mock_get_settings.return_value = "bct"
        self.mock_fbenv_layer_cook.side_effect = Exception()
        self.builder.extract_expression_debugdata()

    @patch("os.path.isfile")
    def test_run_guid_checker(self, mock_isfile):
        """
        Tests DataUtils.run_guid_checker()
        """
        mock_isfile.return_value = True
        self.mock_core_run.return_value = 0, [], []
        self.builder.run_guid_checker()
        self.mock_core_run.assert_called_once_with(
            [
                os.path.join(frostbite_core.get_tnt_root(), "bin", "GuidChecker.exe"),
                frostbite_core.get_game_data_dir(),
            ],
            print_std_out=True,
        )

    @patch("os.path.isfile")
    def test_run_guid_checker_non_default_guid_checker_dir(self, mock_isfile):
        """
        Tests DataUtils.run_guid_checker()
        """
        mock_isfile.return_value = True
        self.mock_core_run.return_value = 0, [], []
        self.builder.run_guid_checker(guid_checker_dir="custom_checker_dir")
        self.mock_core_run.assert_called_once_with(
            [
                os.path.join("custom_checker_dir", "GuidChecker.exe"),
                frostbite_core.get_game_data_dir(),
            ],
            print_std_out=True,
        )

    @patch("os.path.isfile")
    def test_run_guid_checker_non_default_check_dir(self, mock_isfile):
        """
        Tests DataUtils.run_guid_checker()
        """
        mock_isfile.return_value = True
        self.mock_core_run.return_value = 0, [], []
        self.builder.run_guid_checker(check_dir="custom_check_dir")
        self.mock_core_run.assert_called_once_with(
            [
                os.path.join(frostbite_core.get_tnt_root(), "bin", "GuidChecker.exe"),
                "custom_check_dir",
            ],
            print_std_out=True,
        )

    @patch("os.path.isfile")
    def test_run_guid_checker_failure(self, mock_isfile):
        mock_isfile.return_value = False
        with pytest.raises(ELIPYException):
            self.builder.run_guid_checker()

    @patch("elipy2.data.DataUtils.run_frostbite_data_upgrade")
    def test_run_frostbite_data_dupgrade(self, mock_fdu_correct):
        """
        Tests DataUtils.run_frostbite_data_dupgrade()
        """
        self.builder.run_frostbite_data_dupgrade(
            "source_game_data_dir", "dest_game_data_dir", "licensee", "scripts_path"
        )
        mock_fdu_correct.assert_called_once_with(
            "source_game_data_dir", "dest_game_data_dir", "licensee", "scripts_path"
        )

    @patch("elipy2.local_paths.get_fdu_folder")
    @patch("elipy2.frostbite_core.get_game_root")
    def test_run_frostbite_data_upgrade_default(self, mock_game_root, mock_fdu_path):
        """
        Tests DataUtils.run_frostbite_data_upgrade()
        """
        mock_game_root.return_value = "game_root"
        mock_fdu_path.return_value = "path"
        self.mock_core_run.return_value = 0, [b"ef"], [b"fe"]
        self.builder.run_frostbite_data_upgrade(
            "source_game_data_dir", "dest_game_data_dir", "licensee", "scripts_path"
        )

        fdu_exe_path = os.path.join("path", "bin", "FrostbiteDatabaseUpgrader.exe")
        self.mock_core_run.assert_called_once_with(
            [
                fdu_exe_path,
                "/SOURCE",
                "source_game_data_dir",
                "/DEST",
                "dest_game_data_dir",
                "/GAMEROOT",
                "game_root",
                "/SCRIPTS",
                "scripts_path",
                "/VERBOSE",
                "/FULLUPGRADE",
                "/LICENSEE",
                "licensee",
                "/GENERATETYPEDB",
                "/SOURCECONTROL",
                "/NOSOURCEMODULES",
                "/WRITE",
                "/PURGE",
            ],
            print_std_out=True,
        )

    @patch("elipy2.local_paths.get_fdu_folder")
    @patch("elipy2.frostbite_core.get_game_root")
    def test_run_frostbite_data_upgrade_extra_args(self, mock_game_root, mock_fdu_path):
        """
        Tests DataUtils.run_frostbite_data_upgrade()
        """
        mock_game_root.return_value = "game_root"
        mock_fdu_path.return_value = "path"
        self.mock_core_run.return_value = 0, [b"ef"], [b"fe"]
        self.builder.run_frostbite_data_upgrade(
            "source_game_data_dir",
            "dest_game_data_dir",
            "licensee",
            "scripts_path",
            ["fdu_arg_1", "fdu_arg_2"],
        )

        fdu_exe_path = os.path.join("path", "bin", "FrostbiteDatabaseUpgrader.exe")
        self.mock_core_run.assert_called_once_with(
            [
                fdu_exe_path,
                "/SOURCE",
                "source_game_data_dir",
                "/DEST",
                "dest_game_data_dir",
                "/GAMEROOT",
                "game_root",
                "/SCRIPTS",
                "scripts_path",
                "/VERBOSE",
                "fdu_arg_1",
                "fdu_arg_2",
            ],
            print_std_out=True,
        )

    def test_get_clean_master_version_args(self):
        """
        Test DataUtils.get_clean_master_version_args()
        """
        args = DataUtils.get_clean_master_version_args()
        assert isinstance(args, list)

    def test_clear_cache_no_args(self):
        self.builder.clear_cache()
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"], pipeline_args=["-f", "clearCache"]
        )

    def test_clear_cache_extra_args(self):
        self.builder.clear_cache(pipeline_args=["arg1", "arg2"])
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"], pipeline_args=["arg1", "arg2", "-f", "clearCache"]
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    @patch("elipy2.data.DataUtils.run_indexing", MagicMock())
    @patch("elipy2.data.DataUtils.response_cooking")
    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_cook_response_file(self, mock_chunk_cooking, mock_response_cooking):
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
        ]
        self.builder.cook(
            assets=test_assets,
            use_response_file=True,
        )
        assert self.mock_fbenv_layer_cook.call_count == 0
        mock_chunk_cooking.assert_not_called()
        mock_response_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["-emitSuperbundles"]
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    @patch("elipy2.data.DataUtils.run_indexing", MagicMock())
    @patch("elipy2.data.DataUtils.response_cooking")
    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_cook_few_assets(self, mock_chunk_cooking, mock_response_cooking, mocker):
        mock_logger = mocker.patch("logging.Logger.info", return_value=None)
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
        ]
        self.builder.cook(
            assets=test_assets,
            asset_chunk_size=10,
        )
        msg = mock_logger.mock_calls[2][1][0]
        params = mock_logger.mock_calls[2][1][1:]
        assert (
            msg % tuple(params)
            == f"Cooking {len(test_assets)} asset(s) for platform win64 on source layer: {' '.join(test_assets)}"
        )
        assert self.mock_fbenv_layer_cook.call_count == 0
        mock_response_cooking.assert_not_called()
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["-emitSuperbundles"], asset_chunk_size=10
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    @patch("elipy2.data.DataUtils.run_indexing", MagicMock())
    @patch("elipy2.data.DataUtils.response_cooking")
    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_cook_few_assets_content_layer(self, mock_chunk_cooking, mock_response_cooking, mocker):
        mock_logger = mocker.patch("logging.Logger.info", return_value=None)
        layer = "C3B2S5"
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
        ]
        self.builder.cook(
            assets=test_assets,
            asset_chunk_size=10,
            pipeline_args=["-emitSuperbundles", "-activeContentLayer", layer],
        )
        msg = mock_logger.mock_calls[2][1][0]
        params = mock_logger.mock_calls[2][1][1:]
        assert (
            msg % tuple(params)
            == f"Cooking {len(test_assets)} asset(s) for platform win64 on content layer '{layer}': {' '.join(test_assets)}"
        )
        assert self.mock_fbenv_layer_cook.call_count == 0
        mock_response_cooking.assert_not_called()
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets,
            pipeline_args=["-emitSuperbundles", "-activeContentLayer", layer],
            asset_chunk_size=10,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    @patch("elipy2.data.DataUtils.run_indexing", MagicMock())
    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_cook_many_assets(self, mock_chunk_cooking):
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
            "asset11",
        ]
        self.builder.cook(
            assets=test_assets,
            asset_chunk_size=10,
        )
        assert self.mock_fbenv_layer_cook.call_count == 0
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["-emitSuperbundles"], asset_chunk_size=10
        )

    def test_response_cooking(self):
        # Don't let function remove response file so we can validate the data later
        os_remove = os.remove
        os.remove = MagicMock()
        response_file_name = f"{time.strftime('%Y%m%d-%H%M%S')}-response"
        response_file_path = os.path.join("tnt_root", "Local", response_file_name)
        asset_count = 200
        test_assets = []
        for i in range(0, asset_count):
            test_assets.append(f"asset{i}")

        self.builder.response_cooking(
            assets=test_assets,
            pipeline_args=[],
        )
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"],
            assets=f"@{os.path.join('Local', response_file_name)}",
            pipeline_args=[],
            is_local=True,
        )
        # Ensure function tried to remove response file but it still exists
        os.remove.assert_called_once_with(response_file_path)
        assert os.path.exists(response_file_path) == True
        # Ensure correct number of assets are in the response file and clean it up afterwards
        with open(response_file_path, "r") as response_file:
            assert sum(1 for line in response_file) == asset_count
        os.remove = os_remove
        os.remove(response_file_path)
        assert os.path.exists(response_file_path) == False

    def test_response_file_exists_on_failure(self):
        os_remove = os.remove
        os.remove = MagicMock()
        response_file_name = f"{time.strftime('%Y%m%d-%H%M%S')}-response"
        response_file_path = os.path.join("tnt_root", "Local", response_file_name)
        test_assets = ["asset1", "asset2", "asset3"]

        self.mock_fbenv_layer_cook.side_effect = Exception()
        with pytest.raises(Exception):
            self.builder.response_cooking(
                assets=test_assets,
                pipeline_args=[],
            )
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"],
            assets=f"@{os.path.join('Local', response_file_name)}",
            pipeline_args=[],
            is_local=True,
        )
        assert os.path.exists(response_file_path) == True
        os.remove.assert_not_called()
        os.remove = os_remove
        os.remove(response_file_path)
        assert os.path.exists(response_file_path) == False

    def test_chunk_cooking_few_assets(self):
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
        ]
        self.builder.chunk_cooking(
            assets=test_assets,
            pipeline_args=["-emitSuperbundles"],
            asset_chunk_size=10,
        )
        self.mock_fbenv_layer_cook.assert_called_once_with(
            platforms=["win64"],
            assets=[
                "asset1",
                "asset2",
                "asset3",
                "asset4",
                "asset5",
                "asset6",
                "asset7",
                "asset8",
                "asset9",
                "asset10",
            ],
            pipeline_args=["-emitSuperbundles"],
            is_local=True,
        )

    def test_chunk_cooking_many_assets(self):
        test_assets = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
            "asset11",
        ]
        self.builder.chunk_cooking(
            assets=test_assets,
            pipeline_args=["-emitSuperbundles"],
            asset_chunk_size=10,
        )
        assert self.mock_fbenv_layer_cook.call_count == 2
        self.mock_fbenv_layer_cook.assert_has_calls(
            [
                call(
                    platforms=["win64"],
                    assets=[
                        "asset1",
                        "asset2",
                        "asset3",
                        "asset4",
                        "asset5",
                        "asset6",
                        "asset7",
                        "asset8",
                        "asset9",
                        "asset10",
                    ],
                    pipeline_args=["-emitSuperbundles"],
                    is_local=True,
                ),
                call(
                    platforms=["win64"],
                    assets=["asset11"],
                    pipeline_args=["-emitSuperbundles"],
                    is_local=True,
                ),
            ]
        )

    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_pipeline_args_default(self, mock_chunk_cooking):
        test_assets = ["asset1", "asset2"]
        self.builder.cook(assets=test_assets)
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["-emitSuperbundles"], asset_chunk_size=50
        )

    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_pipeline_args_include_arg(self, mock_chunk_cooking):
        test_assets = ["asset1", "asset2"]
        self.builder.cook(assets=test_assets, pipeline_args=["arg1"])
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["arg1", "-emitSuperbundles"], asset_chunk_size=50
        )

    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_skip_trim(self, mock_chunk_cooking):
        test_assets = ["asset1", "asset2"]
        self.builder.cook(assets=test_assets, trim=False)
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=[], asset_chunk_size=50
        )

    @patch("elipy2.data.DataUtils.chunk_cooking")
    def test_pipeline_args_trim_already_included(self, mock_chunk_cooking):
        test_assets = ["asset1", "asset2"]
        self.builder.cook(assets=test_assets, pipeline_args=["-emitSuperbundles"])
        mock_chunk_cooking.assert_called_once_with(
            assets=test_assets, pipeline_args=["-emitSuperbundles"], asset_chunk_size=50
        )

    def test_data_get_content_layer_empty(self):
        """
        Tests DataUtils.get_content_layer() when pipeline_args is empty.
        """
        assert self.builder.get_content_layer() is None

    def test_data_get_content_layer_not_set(self):
        """
        Tests DataUtils.get_content_layer() when -activeContentLayer is not set.
        """
        assert self.builder.get_content_layer(["-clean", "-updateIndex"]) is None

    def test_data_get_content_layer_missing(self):
        """
        Tests DataUtils.get_content_layer() when -activeContentLayer is set but a layer is not provided.
        """
        assert (
            self.builder.get_content_layer(["-clean", "-updateIndex", "-activeContentLayer"])
            is None
        )

    def test_data_get_content_layer_end_case_insensitive(self):
        """
        Tests DataUtils.get_content_layer() when content layer is at the end of the args list and doesn't have consistent case.
        """
        content_layer = "test_layer"
        assert (
            self.builder.get_content_layer(
                ["-clean", "-updateIndex", "-activecontentLayer", content_layer]
            )
            == content_layer
        )

    def test_data_get_content_layer_middle(self):
        """
        Tests DataUtils.get_content_layer() when content layer is in the middle of the args list.
        """
        content_layer = "test_layer"
        assert (
            self.builder.get_content_layer(
                ["-clean", "-activeContentLayer", content_layer, "-updateIndex"]
            )
            == content_layer
        )


class TestDataInit(object):
    def setup(self):
        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

        self.patcher_is_buildsystem_run = patch("elipy2.core.is_buildsystem_run")
        self.mock_is_buildsystem_run = self.patcher_is_buildsystem_run.start()

        self.patcher_set_monkey_build_label = patch("elipy2.frostbite_core.set_monkey_build_label")
        self.mock_set_monkey_build_label = self.patcher_set_monkey_build_label.start()

    def teardown(self):
        patch.stopall()

    def test_data_init_local(self):
        self.mock_is_buildsystem_run.return_value = False
        elipy2.data.DataUtils("win64", ["testlevels"])
        self.mock_is_buildsystem_run.assert_called_once_with()
        assert self.mock_ensure_p4_config.call_count == 0
        assert self.mock_set_monkey_build_label.call_count == 0

    def test_data_init_buildfarm_default(self):
        self.mock_is_buildsystem_run.return_value = True
        elipy2.data.DataUtils("win64", ["testlevels"])
        self.mock_set_monkey_build_label.assert_called_once_with(None)
        self.mock_ensure_p4_config.assert_called_once_with()

    def test_data_init_buildfarm_monkey_build_label(self):
        self.mock_is_buildsystem_run.return_value = True
        elipy2.data.DataUtils("win64", ["testlevels"], monkey_build_label="1234")
        self.mock_set_monkey_build_label.assert_called_once_with("1234")
        self.mock_ensure_p4_config.assert_called_once_with()

    def test_data_init_buildfarm_skip_overwrite_p4config(self):
        self.mock_is_buildsystem_run.return_value = True
        elipy2.data.DataUtils("win64", ["testlevels"], overwrite_p4config=False)
        self.mock_set_monkey_build_label.assert_called_once_with(None)
        assert self.mock_ensure_p4_config.call_count == 0
