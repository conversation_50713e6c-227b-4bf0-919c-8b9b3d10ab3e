package scripts.schedulers

import com.cloudbees.groovy.cps.NonCPS
import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import hudson.model.Job
import hudson.model.Result
import hudson.model.StreamBuildListener
import hudson.util.NullStream
import jenkins.model.Jenkins
import jenkins.model.JenkinsLocationConfiguration

/**
 * autoMaintenanceAgent.groovy
 * This job is used to loop over Jenkins jobs and check if the last build was failed
 * with a specific BFA message then triggering offline.agent job with tainting message.
 * This job can be manually triggered or based on a cron timer.
 */

stage('Automaintenance Node if failing with un-recovered BFA') {
    node('master') {
        maintenanceNode()
    }
}

@NonCPS
void maintenanceNode() {
    def jenkinsUrl = JenkinsLocationConfiguration.get().url
    if (jenkinsUrl.contains('preflight')) {
        echo 'Automaintenance should not be run on preflight master. Aborted'
    } else {
        def allJobs = Jenkins.get().getAllItems(Job)
        def instance = hudson.model.Hudson.instance

        // Handle cloud VMs waiting for executors
        handleWaitingCloudNodes(allJobs, instance)

        // Iterate over all jobs for BFA failures
        allJobs.each { job ->
            // Get the last build
            def build = job.lastCompletedBuild
            def jobName = job.name
            // Check if the last build failed
            if (build != null && build.result == Result.FAILURE && !jobName.contains('start')) {
                def foundFailureCauses = build.getAction(FailureCauseBuildAction)?.foundFailureCauses
                if (foundFailureCauses) {
                    for (cause in foundFailureCauses) {
                        echo 'Processing Job ' + jobName + ' ...'
                        def tainted = actionOnNodeFromBuild(cause, build, instance)
                        def rebooted = actionOnNodeFromBuild(cause, build, instance, action = 'reboot')
                        def avalanche = actionOnNodeFromBuild(cause, build, instance, action = 'avalanche')
                        def runCommand = actionOnNodeFromBuild(cause, build, instance, action = 'run_command')
                        def upstreamJob = findUpstreamSchedulerJob(build, instance)
                        if (upstreamJob && (tainted || rebooted || avalanche || runCommand)) {
                            rebuildJob(upstreamJob)
                        }
                    }
                }
            }
        }
    }
}

@NonCPS
void handleWaitingCloudNodes(allJobs, instance) {
    echo 'Checking for jobs waiting for cloud VM executors...'
    def thirtyMinutesAgo = System.currentTimeMillis() - (30 * 60 * 1000) // 30 minutes in milliseconds

    allJobs.each { job ->
        processQueuedJob(job, thirtyMinutesAgo, instance)
        processBuildingJob(job, thirtyMinutesAgo)
    }
}

@NonCPS
void processQueuedJob(job, thirtyMinutesAgo, instance) {
    // Skip agent.reboot job since it's designed to reboot nodes, not remove them
    if (job.name == 'agent.reboot') {
        return
    }

    // Check if job is currently building and waiting in queue
    if (job.inQueue) {
        def queueItem = job.queueItem
        if (queueItem != null) {
            def queueTime = queueItem.inQueueSince
            def assignedLabel = queueItem.assignedLabel

            // Check if waiting for more than 30 minutes
            if (queueTime < thirtyMinutesAgo) {
                def waitingMinutes = (System.currentTimeMillis() - queueTime) / (1000 * 60)
                echo 'Job ' + job.name + ' has been waiting for ' + waitingMinutes + ' minutes'

                // Check if waiting for a specific node or label
                if (assignedLabel != null) {
                    handleWaitingJobWithLabel(job, assignedLabel, instance)
                }
            }
        }
    }
}

@NonCPS
void handleWaitingJobWithLabel(job, assignedLabel, instance) {
    def labelString = assignedLabel.toString()
    echo 'Job ' + job.name + ' is waiting for label: ' + labelString

    // Find nodes matching this label that are offline cloud VMs
    def matchingNodes = instance.nodes.findAll { node ->
        node.labelString.contains(labelString) &&
        isCloudNode(node) &&
        !node.toComputer().online
    }

    matchingNodes.each { node ->
        def nodeName = node.nodeName
        echo 'Found offline cloud VM ' + nodeName + ' matching waiting job ' + job.name
        if (removeNode(nodeName, true)) {
            echo 'Successfully removed cloud VM ' + nodeName + ' that was blocking job ' + job.name
        }
    }
}

@NonCPS
void processBuildingJob(job, thirtyMinutesAgo) {
    // Skip agent.reboot job since it's designed to reboot nodes, not remove them
    if (job.name == 'agent.reboot') {
        return
    }

    // Also check for builds that are waiting for available executor
    if (job.building) {
        def currentBuild = job.lastBuild
        if ((currentBuild?.building)) {
            def executor = currentBuild.executor
            if (executor == null) {
                // Build is in progress but no executor assigned - might be waiting
                def buildStartTime = currentBuild.startTimeInMillis
                if (buildStartTime < thirtyMinutesAgo) {
                    def waitingMinutes = (System.currentTimeMillis() - buildStartTime) / (1000 * 60)
                    echo ' Build ' + currentBuild.displayName + ' of job ' + job.name + ' has been waiting for executor for ' + waitingMinutes + ' minutes'
                }
            }
        }
    }
}

@NonCPS
Boolean isCloudNode(node) {
    def nodeLabel = node.labelString

    // Identify cloud nodes by checking labels
    def cloudPatterns = [
        'cloud',
        'frosted',
        'managed_cloud',
    ]

    return nodeLabel && cloudPatterns.any { pattern ->
        nodeLabel.toLowerCase().contains(pattern)
    }
}

@NonCPS
Boolean removeNode(nodeName, Boolean cloudOnly = false) {
    try {
        def node = Jenkins.get().getNode(nodeName)
        if (node) {
            // If cloudOnly is true, verify it's a cloud node
            if (cloudOnly && !isCloudNode(node)) {
                echo 'Node ' + nodeName + ' is not a cloud VM, skipping removal'
                return false
            }

            def nodeType = cloudOnly ? 'cloud VM' : 'node'
            echo 'Found ' + nodeType + ':' + nodeName + ' - Attempting to delete...'
            node.toComputer().doDoDelete()
            echo nodeType.capitalize() + nodeName + ' has been deleted successfully'
            return true
        }
        echo 'Node ' + nodeName + ' not found'
        return false
    } catch (Exception e) {
        echo 'Error removing node ' + nodeName + ' : ' + e.message
        return false
    }
}

@NonCPS
Job findUpstreamSchedulerJob(currentBuild, instance) {
    def upstreamJobName = currentBuild.getCause(hudson.model.Cause$UpstreamCause)?.upstreamProject
    if (upstreamJobName?.contains('.start')) {
        def upstreamJob = instance.getItemByFullName(upstreamJobName)
        return upstreamJob
    }
    return null
}

@NonCPS
Boolean actionOnNodeFromBuild(cause, build, instance, action = 'taint') {
    def listExcludedNode = ['ro-sync']
    def excludedLabel = 'managed_cloud_recycled'

    if (cause?.categories?.toString()?.contains(getBfaCategory(action))) {
        echo 'Build  ' + build.displayName + ' failed with "' + cause.name.toString() + '"'
        def listener = new StreamBuildListener(new NullStream())
        def nodeName = build.getEnvironment(listener).get('NODE_NAME').toString()
        if (nodeName != 'null' && !listExcludedNode.contains(nodeName)) {
            def node = instance.getNode(nodeName)?.computer
            def nodeLabel = instance.getNode(nodeName)?.labelString
            if (node && node?.online && isNodeInPool(nodeLabel)) {
                if (nodeLabel.contains(excludedLabel)) {
                    echo nodeName + ' need to be deleted ...'
                    return removeNode(nodeName)
                }
                if (!nodeLabel.contains(excludedLabel)) {
                    echo nodeName + ' need to be ' + action + 'ed ...'

                    // For run_command action, extract the command from the BFA comment
                    if (action == 'run_command') {
                        def command = cause.comment?.trim()
                        if (command) {
                            echo 'Command to execute: ' + command
                            return actionOnNode(node, nodeName, action, command)
                        }
                        echo 'No command specified in BFA comment'
                        return false
                    }
                    return actionOnNode(node, nodeName, action)
                }
            }
        }
    }
    return false
}

@NonCPS
void rebuildJob(upstreamJob) {
    echo 'Job ' + upstreamJob.name + ' need to rebuild '
    if (!upstreamJob.building) {
        // Preserve original build parameters to prevent self-triggering loops
        def lastBuild = upstreamJob.lastBuild
        def originalParams = lastBuild?.getAction(hudson.model.ParametersAction)

        if (originalParams) {
            // Rebuild with original parameters preserved
            upstreamJob.scheduleBuild(0, new hudson.model.Cause.RemoteCause(env.BUILD_URL, 'BFA retry'), originalParams)
            echo 'Job ' + upstreamJob.name + ' are rebuilding last failed build with original parameters ... '
        } else {
            // Fallback for non-parameterized jobs
            upstreamJob.scheduleBuild(new hudson.model.Cause.UpstreamCause(upstreamJob.lastBuild))
            echo 'Job ' + upstreamJob.name + ' are rebuilding last failed build ... '
        }
    }
}

@NonCPS
String getBfaCategory(action) {
    return (action == 'reboot') ? 'autoreboot' : (action == 'avalanche') ? 'restart_avalanche' : (action == 'run_command') ? 'run_command' : 'taint'
}

@NonCPS
Boolean actionOnNode(node, nodeName, action, command = null) {
    if (action == 'reboot') {
        return rebootNode(nodeName)
    } else if (action == 'avalanche') {
        return avalancheMaintenanceNode(nodeName)
    } else if (action == 'run_command') {
        return runCommandOnAgent(nodeName, command)
    } else if (action == 'taint') {
        return taintNodeOffline(node, nodeName)
    }
}

@NonCPS
Boolean taintNodeOffline(node, nodeName) {
    def offlineReason = '[Taint] NeedsTainting'
    node.doToggleOffline(offlineReason)
    echo nodeName + ' is marked offline with ' + offlineReason
    return true
}

@NonCPS
Boolean rebootNode(nodeName) {
    if (Jenkins.get().getItem('agent.reboot')) {
        build wait: false, propagate: false, job: 'agent.reboot', parameters: [
            [
                $class         : 'NodeParameterValue',
                labels         : ["$nodeName"],
                name           : 'machine',
                nodeEligibility: [$class: 'AllNodeEligibility']
            ]
        ]
    } else {
        echo 'Job agent.reboot not found'
        return false
    }
    return true
}

@NonCPS
Boolean avalancheMaintenanceNode(nodeName) {
    if (Jenkins.get().getItem('avalanche_maintenance')) {
        build wait: false, propagate: false, job: 'avalanche_maintenance', parameters: [
            [
                $class         : 'NodeParameterValue',
                labels         : ["$nodeName"],
                name           : 'machine',
                nodeEligibility: [$class: 'AllNodeEligibility']
            ]
        ]
    } else {
        echo 'Job avalanche_maintenance not found'
        return false
    }
    return true
}

@NonCPS
Boolean runCommandOnAgent(nodeName, command) {
    if (Jenkins.get().getItem('runCommandOnAgent')) {
        build wait: false, propagate: false, job: 'runCommandOnAgent', parameters: [
            [
                $class         : 'NodeParameterValue',
                labels         : ["$nodeName"],
                name           : 'machine',
                nodeEligibility: [$class: 'AllNodeEligibility']
            ],
            [
                $class: 'StringParameterValue',
                name  : 'command',
                value : command ?: 'echo "No command specified"'
            ]
        ]
        echo "Running command '${command}' on node ${nodeName}"
    } else {
        echo 'Job runCommandOnAgent not found'
        return false
    }
    return true
}

@NonCPS
Boolean isNodeInPool(label) {
    def nodes = Jenkins.get().nodes
    def nodesWithLabel = nodes.findAll { node ->
        node.labelString.contains(label)
    }
    if (nodesWithLabel.size() > 1) {
        return true
    }
    echo 'Node with label ' + label + ' is not in pool'
    return false
}
