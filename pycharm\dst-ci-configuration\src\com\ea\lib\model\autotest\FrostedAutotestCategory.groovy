package com.ea.lib.model.autotest

class FrostedAutotestCategory extends AutotestCategory {

    private static final String FROSTED_P4_SERVER = 'ssl:uswest2-p4buildedge-fb.p4one.ea.com:2001'

    FrostedAutotestCategory() {
        super()
        this.p4CodeServer = FROSTED_P4_SERVER
        this.p4DataServer = FROSTED_P4_SERVER
    }

    /**
     * Whether or not it's a FrostEd Autotest. Returns true.
     */
    final boolean isFrostedAutotest = true
    /**
     * FrostEd Autotests always have parallelLimit set to 1. One job, one platform: win64. Returns 1.
     */
    final int parallelLimit = 1
    /**
     * We want to run each test suite as its own job (COBRA-5309) that is why we don't run FrostEd Autotest run in parallel. Returns false.
     */
    final Boolean runLevelsInParallel = false

}
