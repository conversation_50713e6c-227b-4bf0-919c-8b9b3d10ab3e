13:12:48 2025-07-11 12:12:48 elipy2 [INFO]: elipy2 version: 17.3a1.dev9064
13:12:48 2025-07-11 12:12:48 elipy2 [INFO]: dice_elipy_scripts version: 10.2.14101
13:12:48 2025-07-11 12:12:48 elipy2 [INFO]: performaing a --dry-run? True
13:12:48 2025-07-11 12:12:48 elipy2 [INFO]: Skipping path_retention files
13:12:48 2025-07-11 12:12:48 elipy2 [INFO]: Selected categories are: {'frosty\\BattlefieldGame': [{'default': 5}, {'ch1-code-dev': 10}, {'ch1-content-dev': 30}, {'ch1-stage': 20}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'frosty\\Frostbite': [{'default': 5}], 'code': [{'default': 40}, {'ch1-content-dev': 100}, {'ch1-marketing-dev': 100}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'webexport': [{'default': 50}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'expressiondebugdata\\BattlefieldGame': [{'default': 50}, {'bflabs': 0}], 'expressiondebugdata\\Frostbite': [{'default': 50}], 'tnt_local': [{'default': 0}]}
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: Disk-based discovery found 38 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-content-dev', 'CH1-SP-content-dev-disc-build', 'CH1-SP-content-dev-first-patch', 'CH1-SP-stage', 'CH1-bflabs-stage', 'CH1-code-dev', 'CH1-content-dev', 'CH1-content-dev-C1S2B1', 'CH1-content-dev-disc-build', 'CH1-content-dev-first-patch', 'CH1-playtest', 'CH1-playtest-gnt', 'CH1-playtest-maps', 'CH1-playtest-san', 'CH1-playtest-san-s2', 'CH1-playtest-sp', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-stage-playtest-sp', 'CH1-to-trunk', 'bf-playtest-gnt', 'bf-playtest-maps', 'bf-playtest-san', 'bf-playtest-sp', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'glacier-sku-transition', 'task1', 'task2', 'task3', 'trunk-code-dev', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-playtest', 'trunk-to-dev-na']
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 30 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 10 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 20 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
13:12:49 2025-07-11 12:12:48 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 3 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
13:12:49 2025-07-11 12:12:49 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
13:13:50 2025-07-11 12:13:50 elipy2 [INFO]: Nothing to delete! builds available 3 <= 5 maximum builds
13:13:50 2025-07-11 12:13:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
13:13:51 2025-07-11 12:13:51 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
13:14:06 2025-07-11 12:14:06 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:14:06 2025-07-11 12:14:06 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
13:14:07 2025-07-11 12:14:07 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:14:07 2025-07-11 12:14:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
13:14:08 2025-07-11 12:14:08 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:14:08 2025-07-11 12:14:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
13:14:08 2025-07-11 12:14:08 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
13:14:09 2025-07-11 12:14:09 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
13:14:10 2025-07-11 12:14:10 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
13:14:13 2025-07-11 12:14:13 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:14:13 2025-07-11 12:14:13 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
13:14:15 2025-07-11 12:14:15 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
13:14:27 2025-07-11 12:14:27 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:14:27 2025-07-11 12:14:27 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
13:14:28 2025-07-11 12:14:28 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:14:28 2025-07-11 12:14:28 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
13:14:28 2025-07-11 12:14:28 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:14:28 2025-07-11 12:14:28 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
13:14:29 2025-07-11 12:14:29 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:14:29 2025-07-11 12:14:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
13:14:29 2025-07-11 12:14:29 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
13:14:29 2025-07-11 12:14:29 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:14:29 2025-07-11 12:14:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
13:14:30 2025-07-11 12:14:29 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
13:14:30 2025-07-11 12:14:30 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
13:14:30 2025-07-11 12:14:30 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
13:14:30 2025-07-11 12:14:30 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
13:14:52 2025-07-11 12:14:52 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
13:14:52 2025-07-11 12:14:52 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
13:14:53 2025-07-11 12:14:53 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
13:15:09 2025-07-11 12:15:09 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:15:09 2025-07-11 12:15:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
13:15:11 2025-07-11 12:15:11 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
13:15:30 2025-07-11 12:15:30 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:15:30 2025-07-11 12:15:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
13:15:31 2025-07-11 12:15:31 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:15:31 2025-07-11 12:15:31 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
13:15:32 2025-07-11 12:15:32 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
13:15:34 2025-07-11 12:15:34 elipy2 [INFO]: Found 10 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
13:15:49 2025-07-11 12:15:49 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:15:49 2025-07-11 12:15:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
13:15:51 2025-07-11 12:15:51 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
13:15:51 2025-07-11 12:15:51 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:15:51 2025-07-11 12:15:51 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
13:15:53 2025-07-11 12:15:53 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
13:15:55 2025-07-11 12:15:55 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:15:55 2025-07-11 12:15:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
13:15:57 2025-07-11 12:15:56 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:15:57 2025-07-11 12:15:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
13:15:57 2025-07-11 12:15:57 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
13:15:58 2025-07-11 12:15:58 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
13:16:13 2025-07-11 12:16:13 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:16:13 2025-07-11 12:16:13 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
13:16:14 2025-07-11 12:16:14 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
13:16:16 2025-07-11 12:16:16 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:16:16 2025-07-11 12:16:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
13:16:16 2025-07-11 12:16:16 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:16:16 2025-07-11 12:16:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
13:16:17 2025-07-11 12:16:17 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
13:16:19 2025-07-11 12:16:19 elipy2 [INFO]: Found 21 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
13:16:33 2025-07-11 12:16:33 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:16:33 2025-07-11 12:16:33 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
13:16:35 2025-07-11 12:16:35 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
13:16:35 2025-07-11 12:16:35 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
13:16:35 2025-07-11 12:16:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
13:16:37 2025-07-11 12:16:37 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
13:17:09 2025-07-11 12:17:09 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:17:09 2025-07-11 12:17:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
13:17:11 2025-07-11 12:17:11 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
13:17:21 2025-07-11 12:17:21 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:17:21 2025-07-11 12:17:21 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
13:17:22 2025-07-11 12:17:22 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
13:17:36 2025-07-11 12:17:36 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:17:36 2025-07-11 12:17:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
13:17:36 2025-07-11 12:17:36 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:17:36 2025-07-11 12:17:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
13:17:37 2025-07-11 12:17:36 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:17:37 2025-07-11 12:17:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
13:17:37 2025-07-11 12:17:37 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
13:17:38 2025-07-11 12:17:38 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
13:17:42 2025-07-11 12:17:42 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
13:17:42 2025-07-11 12:17:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
13:17:55 2025-07-11 12:17:55 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:17:55 2025-07-11 12:17:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
13:18:07 2025-07-11 12:18:07 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:18:07 2025-07-11 12:18:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
13:18:10 2025-07-11 12:18:10 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:18:10 2025-07-11 12:18:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
13:18:33 2025-07-11 12:18:33 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:18:33 2025-07-11 12:18:33 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
13:18:34 2025-07-11 12:18:34 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
13:18:34 2025-07-11 12:18:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
13:18:42 2025-07-11 12:18:42 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:18:42 2025-07-11 12:18:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
13:18:42 2025-07-11 12:18:42 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
13:18:42 2025-07-11 12:18:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
13:19:42 2025-07-11 12:19:42 elipy2 [INFO]: Nothing to delete! builds available 30 <= 30 maximum builds
13:19:42 2025-07-11 12:19:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
13:19:51 2025-07-11 12:19:51 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
13:19:51 2025-07-11 12:19:51 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
13:19:51 2025-07-11 12:19:51 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
13:19:51 2025-07-11 12:19:51 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage\24333894\CH1-stage\24333894
13:19:51 2025-07-11 12:19:51 elipy2 [INFO]: All planned deletions completed successfully
13:19:52 2025-07-11 12:19:52 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
13:19:52 2025-07-11 12:19:52 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
13:19:52 2025-07-11 12:19:52 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: Disk-based discovery found 54 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-release', 'CH1-event', 'CH1-release', 'ZIPs', 'bflabs', 'ch1-bflabs-qol', 'ch1-bflabs-release', 'ch1-bflabs-stage', 'ch1-code-dev', 'ch1-code-dev-asan', 'ch1-code-dev-clean', 'ch1-code-dev-sanitizers/asan', 'ch1-code-dev-sanitizers/ubsan', 'ch1-content-dev', 'ch1-content-dev-cache', 'ch1-content-dev-clean', 'ch1-content-dev-metrics', 'ch1-content-dev-sanitizers/asan', 'ch1-content-dev-sanitizers/ubsan', 'ch1-marketing-dev', 'ch1-marketing-dev-cache', 'ch1-media-team', 'ch1-playtest', 'ch1-playtest-stage', 'ch1-qol', 'ch1-sp-content-dev', 'ch1-sp-stage', 'ch1-stage', 'ch1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'ecs-splines', 'glacier-sku-transition', 'gnt-proto', 'media-team', 'task1', 'task2', 'task3', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-clean', 'trunk-code-dev-sanitizers/asan', 'trunk-code-dev-sanitizers/ubsan', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-content-dev-skybuild', 'trunk-content-dev-test', 'trunk-playtest', 'trunk-to-dev-na']
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task3
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task1
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task4
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task2
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
13:20:10 2025-07-11 12:20:10 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24323022
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24325788
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24327959
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24330117
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24037366
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24335644
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24038530
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24336637
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24041017
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24349614
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23960235
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24042181
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24353219
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23962304
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24044352
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24355138
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23963269
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24046205
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24357207
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23966316
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24047514
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24359703
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23968940
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24048225
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24364434
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24023699
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24051370
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24366345
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24024280
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24052722
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24367988
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24037412
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24054838
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24369345
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24401195
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24056933
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24371701
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24401749
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24058390
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24373616
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24403388
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24059367
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 11 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan - scanning only direct CL directories
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan: 11 total builds found (11 direct, 0 in subdirs)
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 11 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24376287
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24061418
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24377962
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24062294
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24379875
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24066880
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24381806
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24068364
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 18 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan - scanning only direct CL directories
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan: 18 total builds found (18 direct, 0 in subdirs)
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24383152
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 18 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24385284
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24387321
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24389566
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24391091
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 24 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24392443
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24393779
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24395224
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24396717
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24397983
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24399318
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24401099
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24403673
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24405962
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24407804
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24409551
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24411070
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24412155
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24413544
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 40 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan - scanning only direct CL directories
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan: 40 total builds found (40 direct, 0 in subdirs)
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
13:20:11 2025-07-11 12:20:11 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
13:21:32 2025-07-11 12:21:32 elipy2 [INFO]: Nothing to delete! builds available 4 <= 40 maximum builds
13:21:32 2025-07-11 12:21:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
13:21:34 2025-07-11 12:21:34 elipy2 [INFO]: Found 44 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
13:23:53 2025-07-11 12:23:53 elipy2 [INFO]: Nothing to delete! builds available 11 <= 40 maximum builds
13:23:53 2025-07-11 12:23:53 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
13:23:54 2025-07-11 12:23:54 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
13:23:54 2025-07-11 12:23:54 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
13:23:54 2025-07-11 12:23:54 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
13:23:55 2025-07-11 12:23:55 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
13:24:39 2025-07-11 12:24:39 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
13:24:39 2025-07-11 12:24:39 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
13:24:40 2025-07-11 12:24:40 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
13:25:01 2025-07-11 12:25:01 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
13:25:01 2025-07-11 12:25:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23962304
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23963269
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23966316
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23968940
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24023699
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24024280
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24037412
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049109
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049808
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24065606
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24066631
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24067148
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24135898
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24136311
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24178746
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24194643
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24209162
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24224232
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278534
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278823
13:25:03 2025-07-11 12:25:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24322397
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325245
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325976
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24353815
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24353887
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24355906
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24380100
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24386101
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24386516
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24387780
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389005
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389401
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389751
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24401195
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24401749
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24403388
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found 36 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan - scanning only direct CL directories
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan: 36 total builds found (36 direct, 0 in subdirs)
13:25:04 2025-07-11 12:25:04 elipy2 [INFO]: Found 36 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
13:26:23 2025-07-11 12:26:23 elipy2 [INFO]: Nothing to delete! builds available 18 <= 40 maximum builds
13:26:23 2025-07-11 12:26:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
13:26:25 2025-07-11 12:26:25 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
13:28:22 2025-07-11 12:28:22 elipy2 [INFO]: Nothing to delete! builds available 24 <= 40 maximum builds
13:28:22 2025-07-11 12:28:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
13:28:23 2025-07-11 12:28:23 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
13:30:29 2025-07-11 12:30:29 elipy2 [INFO]: Nothing to delete! builds available 30 <= 40 maximum builds
13:30:29 2025-07-11 12:30:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
13:30:30 2025-07-11 12:30:30 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
13:33:40 2025-07-11 12:33:40 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:33:40 2025-07-11 12:33:40 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
13:33:41 2025-07-11 12:33:41 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
13:34:05 2025-07-11 12:34:05 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:34:05 2025-07-11 12:34:05 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24311642
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24313090
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24315995
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24318068
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24320228
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24321590
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24323022
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24325788
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24327959
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24330117
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24335644
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24349614
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24353219
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24355138
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24357207
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24359703
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24364434
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24366345
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24367988
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24369345
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24379875
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24381806
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24383152
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24385284
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24387321
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24389566
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24391091
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24392443
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24393779
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24395224
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24396717
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24397983
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24399318
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24401099
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24403673
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24405962
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24407804
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24409551
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24411070
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24412155
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24413544
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found 41 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan - scanning only direct CL directories
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan: 41 total builds found (41 direct, 0 in subdirs)
13:34:06 2025-07-11 12:34:06 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
13:34:07 2025-07-11 12:34:07 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:34:07 2025-07-11 12:34:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
13:34:08 2025-07-11 12:34:08 elipy2 [INFO]: Found 21 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
13:34:35 2025-07-11 12:34:35 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23836410 (drone build)
13:34:45 2025-07-11 12:34:45 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23809175 (drone build)
13:34:48 2025-07-11 12:34:48 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23853895 (drone build)
13:34:48 2025-07-11 12:34:48 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:34:48 2025-07-11 12:34:48 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
13:34:50 2025-07-11 12:34:50 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
13:34:58 2025-07-11 12:34:58 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23819207 (drone build)
13:34:58 2025-07-11 12:34:58 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:34:58 2025-07-11 12:34:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
13:35:00 2025-07-11 12:35:00 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
13:37:17 2025-07-11 12:37:17 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23607169 (promoted build)
13:37:30 2025-07-11 12:37:30 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23878868 (drone build)
13:37:35 2025-07-11 12:37:35 elipy2 [INFO]: Nothing to delete! builds available 36 <= 40 maximum builds
13:37:35 2025-07-11 12:37:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
13:37:37 2025-07-11 12:37:37 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
13:37:43 2025-07-11 12:37:43 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23880899 (drone build)
13:37:54 2025-07-11 12:37:54 elipy2 [INFO]: Nothing to delete! builds available 12 <= 40 maximum builds
13:37:54 2025-07-11 12:37:54 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23962262
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
13:37:56 2025-07-11 12:37:56 elipy2 [INFO]: All planned deletions completed successfully
13:37:57 2025-07-11 12:37:57 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
13:38:43 2025-07-11 12:38:43 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:38:43 2025-07-11 12:38:43 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
13:38:46 2025-07-11 12:38:46 elipy2 [INFO]: Found 103 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
13:41:26 2025-07-11 12:41:26 elipy2 [INFO]: Nothing to delete! builds available 21 <= 40 maximum builds
13:41:26 2025-07-11 12:41:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
13:41:28 2025-07-11 12:41:28 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
13:41:32 2025-07-11 12:41:32 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23764468 (promoted build)
13:41:44 2025-07-11 12:41:44 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23773826 (drone build)
13:41:57 2025-07-11 12:41:57 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23851036 (drone build)
13:41:57 2025-07-11 12:41:57 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:41:57 2025-07-11 12:41:57 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
13:41:59 2025-07-11 12:41:59 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
13:43:11 2025-07-11 12:43:11 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23882059 (drone build)
13:43:25 2025-07-11 12:43:25 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23883264 (drone build)
13:43:25 2025-07-11 12:43:25 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:43:25 2025-07-11 12:43:25 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
13:43:26 2025-07-11 12:43:26 elipy2 [INFO]: Found 37 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
13:44:33 2025-07-11 12:44:33 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:44:33 2025-07-11 12:44:33 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
13:44:35 2025-07-11 12:44:35 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
13:49:05 2025-07-11 12:49:05 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
13:49:05 2025-07-11 12:49:05 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
13:49:05 2025-07-11 12:49:05 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
13:49:05 2025-07-11 12:49:05 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24311642
13:49:05 2025-07-11 12:49:05 elipy2 [INFO]: All planned deletions completed successfully
13:49:06 2025-07-11 12:49:06 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
13:49:10 2025-07-11 12:49:10 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:49:11 2025-07-11 12:49:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
13:49:13 2025-07-11 12:49:13 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
13:49:25 2025-07-11 12:49:25 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1\22736920 (drone build)
13:49:25 2025-07-11 12:49:25 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:49:25 2025-07-11 12:49:25 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
13:49:27 2025-07-11 12:49:27 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
13:52:16 2025-07-11 12:52:16 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:52:16 2025-07-11 12:52:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
13:52:18 2025-07-11 12:52:18 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
13:52:18 2025-07-11 12:52:18 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
13:52:18 2025-07-11 12:52:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
13:52:19 2025-07-11 12:52:19 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
13:53:16 2025-07-11 12:53:16 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23180978 (drone build)
13:53:31 2025-07-11 12:53:31 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23318395 (drone build)
13:53:31 2025-07-11 12:53:31 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:53:31 2025-07-11 12:53:31 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
13:53:33 2025-07-11 12:53:33 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\23681229 (promoted build)
13:53:34 2025-07-11 12:53:34 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
13:53:46 2025-07-11 12:53:46 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24231509 (drone build)
13:54:00 2025-07-11 12:54:00 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24234018 (drone build)
13:54:00 2025-07-11 12:54:00 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:54:00 2025-07-11 12:54:00 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
13:54:01 2025-07-11 12:54:01 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
13:55:38 2025-07-11 12:55:38 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:55:38 2025-07-11 12:55:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
13:55:40 2025-07-11 12:55:40 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
13:56:47 2025-07-11 12:56:47 elipy2 [INFO]: Nothing to delete! builds available 37 <= 40 maximum builds
13:56:47 2025-07-11 12:56:47 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
13:56:50 2025-07-11 12:56:50 elipy2 [INFO]: Found 105 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
13:57:00 2025-07-11 12:57:00 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk\23569006 (promoted build)
13:57:01 2025-07-11 12:57:01 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
13:57:01 2025-07-11 12:57:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
13:57:03 2025-07-11 12:57:03 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
13:57:24 2025-07-11 12:57:24 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
13:57:24 2025-07-11 12:57:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
13:57:26 2025-07-11 12:57:26 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
13:59:02 2025-07-11 12:59:02 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
13:59:02 2025-07-11 12:59:02 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24037366
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24038530
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24041017
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24042181
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24044352
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24046205
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24047514
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24048225
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24051370
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24052722
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24054838
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24056933
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24058390
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24059367
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24061418
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24062294
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24066880
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24068364
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Found 18 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
13:59:03 2025-07-11 12:59:03 elipy2 [INFO]: Sanitizer subdirectory detected: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan - scanning only direct CL directories
13:59:04 2025-07-11 12:59:04 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan: 18 total builds found (18 direct, 0 in subdirs)
13:59:04 2025-07-11 12:59:04 elipy2 [INFO]: Found 18 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
14:03:10 2025-07-11 13:03:10 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
14:03:10 2025-07-11 13:03:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
14:03:45 2025-07-11 13:03:45 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
14:03:45 2025-07-11 13:03:45 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
14:03:46 2025-07-11 13:03:46 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
14:04:03 2025-07-11 13:04:03 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21796588 (promoted build)
14:04:14 2025-07-11 13:04:14 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21947691 (drone build)
14:04:14 2025-07-11 13:04:14 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
14:04:14 2025-07-11 13:04:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
14:04:15 2025-07-11 13:04:15 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
14:05:12 2025-07-11 13:05:12 elipy2 [INFO]: Nothing to delete! builds available 18 <= 40 maximum builds
14:05:12 2025-07-11 13:05:12 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
14:05:13 2025-07-11 13:05:13 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
14:05:13 2025-07-11 13:05:13 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
14:05:13 2025-07-11 13:05:13 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
14:05:14 2025-07-11 13:05:14 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
14:05:33 2025-07-11 13:05:33 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
14:05:33 2025-07-11 13:05:33 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
14:06:38 2025-07-11 13:06:38 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23138880 (drone build)
14:06:48 2025-07-11 13:06:48 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
14:06:48 2025-07-11 13:06:48 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
14:06:52 2025-07-11 13:06:51 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23141586 (drone build)
14:06:52 2025-07-11 13:06:51 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
14:06:52 2025-07-11 13:06:51 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
14:06:52 2025-07-11 13:06:52 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
14:07:07 2025-07-11 13:07:07 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
14:07:07 2025-07-11 13:07:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
14:07:20 2025-07-11 13:07:20 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
14:07:20 2025-07-11 13:07:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
14:07:54 2025-07-11 13:07:54 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
14:07:54 2025-07-11 13:07:54 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
14:08:24 2025-07-11 13:08:24 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24120608 (drone build)
14:08:31 2025-07-11 13:08:31 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24121042 (drone build)
14:08:31 2025-07-11 13:08:31 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
14:08:31 2025-07-11 13:08:31 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
14:09:16 2025-07-11 13:09:16 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24145276 (promoted build)
14:09:20 2025-07-11 13:09:20 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24253749 (drone build)
14:09:26 2025-07-11 13:09:26 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24254058 (drone build)
14:09:26 2025-07-11 13:09:26 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
14:09:26 2025-07-11 13:09:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
14:09:26 2025-07-11 13:09:26 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
14:09:42 2025-07-11 13:09:42 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
14:09:42 2025-07-11 13:09:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
14:09:42 2025-07-11 13:09:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
14:10:18 2025-07-11 13:10:18 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24241970 (drone build)
14:10:23 2025-07-11 13:10:23 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24242417 (drone build)
14:10:28 2025-07-11 13:10:28 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:10:28 2025-07-11 13:10:28 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:10:28 2025-07-11 13:10:28 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:10:28 2025-07-11 13:10:28 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24385696
14:10:29 2025-07-11 13:10:29 elipy2 [INFO]: All planned deletions completed successfully
14:10:29 2025-07-11 13:10:29 elipy2 [INFO]: Found 7 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
14:11:22 2025-07-11 13:11:22 elipy2 [INFO]: Nothing to delete! builds available 7 <= 40 maximum builds
14:11:22 2025-07-11 13:11:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
14:11:29 2025-07-11 13:11:29 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23470552 (promoted build)
14:11:33 2025-07-11 13:11:33 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23498679 (drone build)
14:11:36 2025-07-11 13:11:36 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23508806 (drone build)
14:11:36 2025-07-11 13:11:36 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
14:11:36 2025-07-11 13:11:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
14:13:41 2025-07-11 13:13:41 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24103633 (drone build)
14:13:44 2025-07-11 13:13:44 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24223891 (drone build)
14:13:47 2025-07-11 13:13:47 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:13:47 2025-07-11 13:13:47 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:13:47 2025-07-11 13:13:47 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:13:47 2025-07-11 13:13:47 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24235171
14:13:47 2025-07-11 13:13:47 elipy2 [INFO]: All planned deletions completed successfully
14:14:29 2025-07-11 13:14:29 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24179982 (promoted build)
14:14:31 2025-07-11 13:14:31 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24222845 (drone build)
14:14:33 2025-07-11 13:14:33 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24223794 (drone build)
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Deletion plan: 0 orphan builds, 2 regular builds (retention)
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Deleting 2 builds (2 regular, 0 orphan)
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Phase 2: Deleting 2 builds based on retention policy
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24370150
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24370838
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: All planned deletions completed successfully
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Disk-based discovery found 12 branches: ['0.1.0', '0.15.0', '0.15.1', '********', '0.15.2', '0.16.0', '0.16.1', '0.16.2', '0.17.0', '0.17.1', '*******', '2.0.0']
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: Disk-based discovery found 29 branches: ['CH1-SP-content-dev', 'CH1-SP-stage', 'CH1-code-dev', 'CH1-code-dev-asan', 'CH1-code-dev-sanitizers', 'CH1-content-dev', 'CH1-content-dev-cache', 'CH1-content-dev-metrics', 'CH1-marketing-dev', 'CH1-playtest', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'media-team', 'task1', 'task2', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-sanitizers', 'trunk-code-dev-skybuild', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-playtest', 'trunk-to-dev-na']
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
14:14:38 2025-07-11 13:14:38 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
14:14:39 2025-07-11 13:14:38 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24003088
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24004119
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24010679
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013106
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013423
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24015920
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24019386
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24021916
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24023774
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24025232
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24027941
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24029263
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24031339
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 13 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Main sanitizer directory detected: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers - ignoring subdirectories (they are handled as separate branches)
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers: 13 total builds found (13 direct, 0 in subdirs)
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 22 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 16 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 20 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 48 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
14:14:39 2025-07-11 13:14:39 elipy2 [INFO]: Found 52 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
14:14:54 2025-07-11 13:14:54 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
14:14:54 2025-07-11 13:14:54 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
14:14:56 2025-07-11 13:14:56 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
14:19:12 2025-07-11 13:19:12 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
14:19:12 2025-07-11 13:19:12 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
14:19:14 2025-07-11 13:19:14 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
14:19:30 2025-07-11 13:19:30 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
14:19:30 2025-07-11 13:19:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
14:19:30 2025-07-11 13:19:30 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
14:19:30 2025-07-11 13:19:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
14:19:31 2025-07-11 13:19:31 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
14:19:32 2025-07-11 13:19:32 elipy2 [INFO]: Found 51 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
14:20:06 2025-07-11 13:20:06 elipy2 [INFO]: Nothing to delete! builds available 16 <= 50 maximum builds
14:20:06 2025-07-11 13:20:06 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
14:20:07 2025-07-11 13:20:07 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
14:20:29 2025-07-11 13:20:29 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
14:20:30 2025-07-11 13:20:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
14:20:31 2025-07-11 13:20:31 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
14:20:47 2025-07-11 13:20:47 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
14:20:47 2025-07-11 13:20:47 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
14:20:49 2025-07-11 13:20:49 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
14:20:53 2025-07-11 13:20:53 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
14:20:53 2025-07-11 13:20:53 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
14:20:54 2025-07-11 13:20:54 elipy2 [INFO]: Found 51 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
14:21:32 2025-07-11 13:21:32 elipy2 [INFO]: Nothing to delete! builds available 20 <= 50 maximum builds
14:21:32 2025-07-11 13:21:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
14:21:33 2025-07-11 13:21:33 elipy2 [INFO]: Found 29 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
14:22:22 2025-07-11 13:22:22 elipy2 [INFO]: Nothing to delete! builds available 22 <= 50 maximum builds
14:22:22 2025-07-11 13:22:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
14:22:22 2025-07-11 13:22:22 elipy2 [INFO]: Found 26 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
14:25:45 2025-07-11 13:25:45 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
14:25:45 2025-07-11 13:25:45 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
14:25:47 2025-07-11 13:25:47 elipy2 [INFO]: Found 21 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
14:31:49 2025-07-11 13:31:49 elipy2 [INFO]: Nothing to delete! builds available 26 <= 50 maximum builds
14:31:49 2025-07-11 13:31:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
14:31:50 2025-07-11 13:31:50 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
14:31:56 2025-07-11 13:31:56 elipy2 [INFO]: Nothing to delete! builds available 48 <= 50 maximum builds
14:31:56 2025-07-11 13:31:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
14:31:57 2025-07-11 13:31:57 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
14:32:22 2025-07-11 13:32:22 elipy2 [INFO]: Nothing to delete! builds available 29 <= 50 maximum builds
14:32:22 2025-07-11 13:32:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
14:32:23 2025-07-11 13:32:23 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
14:32:29 2025-07-11 13:32:29 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
14:32:29 2025-07-11 13:32:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
14:32:30 2025-07-11 13:32:30 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
14:32:30 2025-07-11 13:32:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
14:32:31 2025-07-11 13:32:31 elipy2 [INFO]: Found 11 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23846862
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23904765
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23916815
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23929926
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23931126
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23950869
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23960229
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23962304
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23963269
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23966316
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23968940
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24023699
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24024280
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24037412
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found 14 direct CL directories in sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Main sanitizer directory detected: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers - ignoring subdirectories (they are handled as separate branches)
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers: 14 total builds found (14 direct, 0 in subdirs)
14:32:32 2025-07-11 13:32:32 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
14:33:33 2025-07-11 13:33:33 elipy2 [INFO]: Nothing to delete! builds available 21 <= 50 maximum builds
14:33:33 2025-07-11 13:33:33 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
14:33:33 2025-07-11 13:33:33 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
14:33:47 2025-07-11 13:33:47 elipy2 [INFO]: Nothing to delete! builds available 5 <= 50 maximum builds
14:33:47 2025-07-11 13:33:47 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
14:33:49 2025-07-11 13:33:49 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: Deletion plan: 0 orphan builds, 2 regular builds (retention)
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: Deleting 2 builds (2 regular, 0 orphan)
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: Phase 2: Deleting 2 builds based on retention policy
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24381045
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24381806
14:33:58 2025-07-11 13:33:58 elipy2 [INFO]: All planned deletions completed successfully
14:34:25 2025-07-11 13:34:25 elipy2 [INFO]: Nothing to delete! builds available 6 <= 50 maximum builds
14:34:25 2025-07-11 13:34:25 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
14:36:04 2025-07-11 13:36:04 elipy2 [INFO]: Nothing to delete! builds available 11 <= 50 maximum builds
14:36:04 2025-07-11 13:36:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
14:36:43 2025-07-11 13:36:43 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
14:36:43 2025-07-11 13:36:43 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
14:36:51 2025-07-11 13:36:51 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
14:36:51 2025-07-11 13:36:51 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
14:36:57 2025-07-11 13:36:57 elipy2 [INFO]: Nothing to delete! builds available 12 <= 50 maximum builds
14:36:57 2025-07-11 13:36:57 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev\24363376
14:37:00 2025-07-11 13:37:00 elipy2 [INFO]: All planned deletions completed successfully
14:37:22 2025-07-11 13:37:22 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
14:37:22 2025-07-11 13:37:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
14:37:27 2025-07-11 13:37:27 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:37:27 2025-07-11 13:37:27 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:37:27 2025-07-11 13:37:27 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:37:27 2025-07-11 13:37:27 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage\24269306
14:37:27 2025-07-11 13:37:27 elipy2 [INFO]: All planned deletions completed successfully
14:37:31 2025-07-11 13:37:31 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
14:37:31 2025-07-11 13:37:31 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
14:37:31 2025-07-11 13:37:31 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
14:37:31 2025-07-11 13:37:31 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
14:37:31 2025-07-11 13:37:31 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
14:38:22 2025-07-11 13:38:22 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
14:38:22 2025-07-11 13:38:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
14:38:22 2025-07-11 13:38:22 elipy2 [WARNING]: Path does not exist, skipping branch discovery: \\filer.dice.ad.ea.com\builds\Battlefield\tnt_local
14:38:22 2025-07-11 13:38:22 elipy2 [INFO]: Skipping azure_fileshare_path_retention files
14:38:22 2025-07-11 13:38:22 elipy2 [WARNING]: Skipping Avalanche db deletion.
14:38:22 2025-07-11 13:38:22 elipy2 [WARNING]: Skipping symstore cleanup.

