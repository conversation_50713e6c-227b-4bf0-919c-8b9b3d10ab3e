09:50:46 2025-07-11 08:50:46 elipy2 [INFO]: elipy2 version: 17.3a1.dev9052
09:50:46 2025-07-11 08:50:46 elipy2 [INFO]: dice_elipy_scripts version: 10.2.14089
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: performaing a --dry-run? True
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Skipping path_retention files
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Selected categories are: {'frosty\\BattlefieldGame': [{'default': 5}, {'ch1-code-dev': 10}, {'ch1-content-dev': 30}, {'ch1-stage': 20}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'frosty\\Frostbite': [{'default': 5}], 'code': [{'default': 40}, {'ch1-content-dev': 100}, {'ch1-marketing-dev': 100}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'webexport': [{'default': 50}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'expressiondebugdata\\BattlefieldGame': [{'default': 50}, {'bflabs': 0}], 'expressiondebugdata\\Frostbite': [{'default': 50}], 'tnt_local': [{'default': 0}]}
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Disk-based discovery found 38 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-content-dev', 'CH1-SP-content-dev-disc-build', 'CH1-SP-content-dev-first-patch', 'CH1-SP-stage', 'CH1-bflabs-stage', 'CH1-code-dev', 'CH1-content-dev', 'CH1-content-dev-C1S2B1', 'CH1-content-dev-disc-build', 'CH1-content-dev-first-patch', 'CH1-playtest', 'CH1-playtest-gnt', 'CH1-playtest-maps', 'CH1-playtest-san', 'CH1-playtest-san-s2', 'CH1-playtest-sp', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-stage-playtest-sp', 'CH1-to-trunk', 'bf-playtest-gnt', 'bf-playtest-maps', 'bf-playtest-san', 'bf-playtest-sp', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'glacier-sku-transition', 'task1', 'task2', 'task3', 'trunk-code-dev', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-playtest', 'trunk-to-dev-na']
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 10 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 20 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 30 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
09:50:47 2025-07-11 08:50:47 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
09:50:48 2025-07-11 08:50:48 elipy2 [INFO]: Found 10 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
09:50:48 2025-07-11 08:50:48 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
09:50:48 2025-07-11 08:50:48 elipy2 [INFO]: Found 20 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
09:52:16 2025-07-11 08:52:16 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:52:16 2025-07-11 08:52:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
09:52:17 2025-07-11 08:52:17 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
09:52:18 2025-07-11 08:52:18 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:52:18 2025-07-11 08:52:18 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:52:18 2025-07-11 08:52:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
09:52:18 2025-07-11 08:52:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
09:52:19 2025-07-11 08:52:19 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:52:19 2025-07-11 08:52:19 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
09:52:20 2025-07-11 08:52:20 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
09:52:20 2025-07-11 08:52:20 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
09:52:20 2025-07-11 08:52:20 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:52:20 2025-07-11 08:52:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
09:52:21 2025-07-11 08:52:21 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
09:52:22 2025-07-11 08:52:22 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
09:52:37 2025-07-11 08:52:37 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:52:37 2025-07-11 08:52:37 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
09:52:38 2025-07-11 08:52:38 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
09:52:39 2025-07-11 08:52:39 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:52:39 2025-07-11 08:52:39 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
09:52:41 2025-07-11 08:52:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:52:41 2025-07-11 08:52:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
09:52:41 2025-07-11 08:52:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:52:41 2025-07-11 08:52:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
09:52:41 2025-07-11 08:52:41 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
09:52:42 2025-07-11 08:52:42 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
09:52:42 2025-07-11 08:52:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
09:52:42 2025-07-11 08:52:42 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
09:52:43 2025-07-11 08:52:43 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
09:52:44 2025-07-11 08:52:44 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
09:54:09 2025-07-11 08:54:09 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:09 2025-07-11 08:54:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
09:54:11 2025-07-11 08:54:11 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:11 2025-07-11 08:54:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
09:54:11 2025-07-11 08:54:11 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:11 2025-07-11 08:54:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
09:54:12 2025-07-11 08:54:12 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:54:12 2025-07-11 08:54:12 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
09:54:12 2025-07-11 08:54:12 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
09:54:13 2025-07-11 08:54:13 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
09:54:14 2025-07-11 08:54:14 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:54:14 2025-07-11 08:54:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
09:54:15 2025-07-11 08:54:15 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
09:54:15 2025-07-11 08:54:15 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
09:54:17 2025-07-11 08:54:17 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:17 2025-07-11 08:54:17 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
09:54:17 2025-07-11 08:54:17 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
09:54:18 2025-07-11 08:54:18 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
09:54:27 2025-07-11 08:54:27 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:27 2025-07-11 08:54:27 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
09:54:30 2025-07-11 08:54:30 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
09:54:32 2025-07-11 08:54:32 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
09:54:32 2025-07-11 08:54:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
09:54:32 2025-07-11 08:54:32 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:32 2025-07-11 08:54:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
09:54:33 2025-07-11 08:54:33 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
09:54:34 2025-07-11 08:54:34 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:54:34 2025-07-11 08:54:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
09:54:34 2025-07-11 08:54:34 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
09:54:35 2025-07-11 08:54:35 elipy2 [INFO]: Found 3 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
09:54:35 2025-07-11 08:54:35 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
09:54:35 2025-07-11 08:54:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
09:54:37 2025-07-11 08:54:37 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
09:54:54 2025-07-11 08:54:54 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
09:54:54 2025-07-11 08:54:54 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
09:54:56 2025-07-11 08:54:56 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
09:55:42 2025-07-11 08:55:42 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:55:42 2025-07-11 08:55:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
09:55:43 2025-07-11 08:55:43 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
09:55:44 2025-07-11 08:55:44 elipy2 [INFO]: Nothing to delete! builds available 3 <= 5 maximum builds
09:55:44 2025-07-11 08:55:44 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
09:55:45 2025-07-11 08:55:45 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
09:55:59 2025-07-11 08:55:59 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:55:59 2025-07-11 08:55:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
09:56:01 2025-07-11 08:56:01 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:56:01 2025-07-11 08:56:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
09:56:01 2025-07-11 08:56:01 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
09:56:03 2025-07-11 08:56:03 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
09:56:04 2025-07-11 08:56:04 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:56:04 2025-07-11 08:56:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
09:56:04 2025-07-11 08:56:04 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:56:04 2025-07-11 08:56:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
09:56:05 2025-07-11 08:56:05 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
09:56:24 2025-07-11 08:56:24 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:56:24 2025-07-11 08:56:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
09:56:26 2025-07-11 08:56:26 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:56:26 2025-07-11 08:56:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
09:56:39 2025-07-11 08:56:39 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:56:39 2025-07-11 08:56:39 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
09:57:00 2025-07-11 08:57:00 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:57:00 2025-07-11 08:57:00 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
09:57:09 2025-07-11 08:57:09 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:57:09 2025-07-11 08:57:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
09:57:10 2025-07-11 08:57:10 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
09:57:10 2025-07-11 08:57:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
09:57:11 2025-07-11 08:57:11 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:57:11 2025-07-11 08:57:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
09:57:23 2025-07-11 08:57:23 elipy2 [INFO]: Nothing to delete! builds available 20 <= 20 maximum builds
09:57:23 2025-07-11 08:57:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
09:57:24 2025-07-11 08:57:24 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
09:57:24 2025-07-11 08:57:24 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
09:57:24 2025-07-11 08:57:24 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
09:57:24 2025-07-11 08:57:24 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage\24387163\CH1-bflabs-stage\24387163
09:57:24 2025-07-11 08:57:24 elipy2 [INFO]: All planned deletions completed successfully
09:58:56 2025-07-11 08:58:56 elipy2 [INFO]: Nothing to delete! builds available 30 <= 30 maximum builds
09:58:56 2025-07-11 08:58:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
09:58:56 2025-07-11 08:58:56 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
09:58:56 2025-07-11 08:58:56 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
09:58:56 2025-07-11 08:58:56 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
09:59:14 2025-07-11 08:59:14 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
09:59:14 2025-07-11 08:59:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Disk-based discovery found 54 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-release', 'CH1-event', 'CH1-release', 'ZIPs', 'bflabs', 'ch1-bflabs-qol', 'ch1-bflabs-release', 'ch1-bflabs-stage', 'ch1-code-dev', 'ch1-code-dev-asan', 'ch1-code-dev-clean', 'ch1-code-dev-sanitizers/asan', 'ch1-code-dev-sanitizers/ubsan', 'ch1-content-dev', 'ch1-content-dev-cache', 'ch1-content-dev-clean', 'ch1-content-dev-metrics', 'ch1-content-dev-sanitizers/asan', 'ch1-content-dev-sanitizers/ubsan', 'ch1-marketing-dev', 'ch1-marketing-dev-cache', 'ch1-media-team', 'ch1-playtest', 'ch1-playtest-stage', 'ch1-qol', 'ch1-sp-content-dev', 'ch1-sp-stage', 'ch1-stage', 'ch1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'ecs-splines', 'glacier-sku-transition', 'gnt-proto', 'media-team', 'task1', 'task2', 'task3', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-clean', 'trunk-code-dev-sanitizers/asan', 'trunk-code-dev-sanitizers/ubsan', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-content-dev-skybuild', 'trunk-content-dev-test', 'trunk-playtest', 'trunk-to-dev-na']
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task1
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task4
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task2
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task3
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24309398
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24311642
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24313090
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24315995
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24037366
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24318068
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24038530
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-test
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24037366
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24320228
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24041017
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24038530
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24321590
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24042181
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 7 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24041017
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24323022
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24044352
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24042181
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24325788
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24046205
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24044352
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24327959
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24047514
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24046205
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24330117
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24048225
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24335644
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24047514
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24051370
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24349614
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24048225
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24052722
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24353219
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24051370
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24054838
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24355138
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24052722
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24056933
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24357207
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24054838
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24058390
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24359703
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24056933
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24059367
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24364434
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24058390
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24061418
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24366345
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24059367
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24062294
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24367988
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24061418
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24066880
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24369345
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24062294
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan\24068364
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 18 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan: 18 total builds found (18 direct, 0 in subdirs)
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 18 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24379875
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24066880
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24381806
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan\24068364
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 18 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan: 18 total builds found (18 direct, 0 in subdirs)
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 18 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24383152
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24385284
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24387321
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24389566
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24391091
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24392443
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24393779
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24395224
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24396717
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24397983
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24399318
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24401099
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24403673
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24405962
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24407804
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24409551
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24411070
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24412155
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 41 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan: 41 total builds found (41 direct, 0 in subdirs)
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
09:59:15 2025-07-11 08:59:15 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
10:01:57 2025-07-11 09:01:57 elipy2 [INFO]: Nothing to delete! builds available 7 <= 40 maximum builds
10:01:57 2025-07-11 09:01:57 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
10:01:58 2025-07-11 09:01:58 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
10:01:58 2025-07-11 09:01:58 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
10:01:58 2025-07-11 09:01:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\bflabs
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24321590
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24323022
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24325788
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24327959
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24330117
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24335644
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24336637
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24349614
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24353219
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24355138
10:01:59 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24357207
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24359703
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24364434
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24366345
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24367988
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24369345
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24371701
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24373616
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24376287
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24377962
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24379875
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24381806
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24383152
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24385284
10:02:00 2025-07-11 09:01:59 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24387321
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24389566
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24391091
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24392443
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24393779
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24395224
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24396717
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24397983
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24399318
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24401099
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24403673
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24405962
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24407804
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24409551
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24411070
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24412155
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found 40 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan: 40 total builds found (40 direct, 0 in subdirs)
10:02:00 2025-07-11 09:02:00 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
10:06:04 2025-07-11 09:06:04 elipy2 [INFO]: Nothing to delete! builds available 18 <= 40 maximum builds
10:06:04 2025-07-11 09:06:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
10:06:07 2025-07-11 09:06:06 elipy2 [INFO]: Found 106 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
10:06:09 2025-07-11 09:06:09 elipy2 [INFO]: Nothing to delete! builds available 18 <= 40 maximum builds
10:06:09 2025-07-11 09:06:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
10:06:10 2025-07-11 09:06:10 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
10:06:10 2025-07-11 09:06:10 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
10:06:10 2025-07-11 09:06:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\gnt-proto
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23962304
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23963269
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23966316
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23968940
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24023699
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24024280
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24037412
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049109
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049808
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24065606
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24066631
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24067148
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24135898
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24136311
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24178746
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24194643
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24209162
10:06:12 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24224232
10:06:13 2025-07-11 09:06:12 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278534
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278823
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24322397
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325245
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325976
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24353815
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24353887
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24355906
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24380100
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24386101
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24386516
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24387780
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389005
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389401
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24389751
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24401195
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24401749
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24403388
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found 36 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan: 36 total builds found (36 direct, 0 in subdirs)
10:06:13 2025-07-11 09:06:13 elipy2 [INFO]: Found 36 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
10:14:14 2025-07-11 09:14:14 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
10:14:14 2025-07-11 09:14:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
10:14:15 2025-07-11 09:14:15 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
10:14:25 2025-07-11 09:14:25 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
10:14:25 2025-07-11 09:14:25 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
10:14:26 2025-07-11 09:14:26 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
10:14:52 2025-07-11 09:14:52 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:14:52 2025-07-11 09:14:52 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
10:14:53 2025-07-11 09:14:53 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
10:15:15 2025-07-11 09:15:15 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
10:15:15 2025-07-11 09:15:15 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
10:15:17 2025-07-11 09:15:17 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
10:15:29 2025-07-11 09:15:29 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
10:15:29 2025-07-11 09:15:29 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
10:15:29 2025-07-11 09:15:29 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
10:15:29 2025-07-11 09:15:29 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24309398
10:15:30 2025-07-11 09:15:30 elipy2 [INFO]: All planned deletions completed successfully
10:15:32 2025-07-11 09:15:32 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
10:15:43 2025-07-11 09:15:43 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23809175 (drone build)
10:15:49 2025-07-11 09:15:49 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24103633 (drone build)
10:15:53 2025-07-11 09:15:53 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23882059 (drone build)
10:16:00 2025-07-11 09:15:59 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23819207 (drone build)
10:16:00 2025-07-11 09:15:59 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:16:00 2025-07-11 09:15:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
10:16:02 2025-07-11 09:16:02 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24223891 (drone build)
10:16:02 2025-07-11 09:16:02 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:16:02 2025-07-11 09:16:02 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
10:16:03 2025-07-11 09:16:03 elipy2 [INFO]: Found 104 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
10:16:03 2025-07-11 09:16:03 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
10:16:07 2025-07-11 09:16:07 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23883264 (drone build)
10:16:07 2025-07-11 09:16:07 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:16:07 2025-07-11 09:16:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
10:16:10 2025-07-11 09:16:10 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
10:16:12 2025-07-11 09:16:12 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23764468 (promoted build)
10:16:25 2025-07-11 09:16:25 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23773826 (drone build)
10:16:40 2025-07-11 09:16:40 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23851036 (drone build)
10:16:40 2025-07-11 09:16:40 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:16:40 2025-07-11 09:16:40 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
10:16:42 2025-07-11 09:16:42 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
10:17:34 2025-07-11 09:17:34 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:17:34 2025-07-11 09:17:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
10:17:35 2025-07-11 09:17:35 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
10:20:13 2025-07-11 09:20:13 elipy2 [INFO]: Nothing to delete! builds available 36 <= 40 maximum builds
10:20:13 2025-07-11 09:20:13 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
10:20:15 2025-07-11 09:20:15 elipy2 [INFO]: Found 21 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
10:21:01 2025-07-11 09:21:01 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
10:21:01 2025-07-11 09:21:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
10:21:02 2025-07-11 09:21:02 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
10:25:59 2025-07-11 09:25:59 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
10:25:59 2025-07-11 09:25:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
10:26:00 2025-07-11 09:26:00 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
10:26:44 2025-07-11 09:26:44 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
10:26:44 2025-07-11 09:26:44 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
10:26:45 2025-07-11 09:26:45 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
10:27:50 2025-07-11 09:27:50 elipy2 [INFO]: Nothing to delete! builds available 21 <= 40 maximum builds
10:27:50 2025-07-11 09:27:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
10:27:51 2025-07-11 09:27:51 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
10:28:55 2025-07-11 09:28:55 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:28:55 2025-07-11 09:28:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
10:28:58 2025-07-11 09:28:58 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
10:29:50 2025-07-11 09:29:50 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:29:50 2025-07-11 09:29:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
10:29:51 2025-07-11 09:29:51 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
10:30:24 2025-07-11 09:30:24 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:30:24 2025-07-11 09:30:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
10:30:25 2025-07-11 09:30:25 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
10:30:25 2025-07-11 09:30:25 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23470552 (promoted build)
10:30:40 2025-07-11 09:30:40 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23498679 (drone build)
10:30:52 2025-07-11 09:30:52 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:30:52 2025-07-11 09:30:52 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
10:30:53 2025-07-11 09:30:53 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
10:30:55 2025-07-11 09:30:55 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23508806 (drone build)
10:30:55 2025-07-11 09:30:55 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:30:55 2025-07-11 09:30:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
10:31:10 2025-07-11 09:31:10 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
10:31:10 2025-07-11 09:31:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
10:31:11 2025-07-11 09:31:11 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
10:32:30 2025-07-11 09:32:30 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23607169 (promoted build)
10:32:41 2025-07-11 09:32:41 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1\22736920 (drone build)
10:32:41 2025-07-11 09:32:41 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:32:41 2025-07-11 09:32:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
10:32:43 2025-07-11 09:32:43 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23878868 (drone build)
10:32:44 2025-07-11 09:32:43 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
10:32:55 2025-07-11 09:32:55 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23880899 (drone build)
10:32:55 2025-07-11 09:32:55 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:32:55 2025-07-11 09:32:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
10:32:56 2025-07-11 09:32:56 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
10:32:56 2025-07-11 09:32:56 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
10:32:56 2025-07-11 09:32:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-release
10:32:57 2025-07-11 09:32:57 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
10:33:59 2025-07-11 09:33:59 elipy2 [INFO]: Nothing to delete! builds available 12 <= 40 maximum builds
10:33:59 2025-07-11 09:33:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
10:34:00 2025-07-11 09:34:00 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
10:39:24 2025-07-11 09:39:24 elipy2 [INFO]: Nothing to delete! builds available 30 <= 40 maximum builds
10:39:24 2025-07-11 09:39:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
10:39:25 2025-07-11 09:39:25 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
10:40:36 2025-07-11 09:40:36 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:40:36 2025-07-11 09:40:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
10:40:37 2025-07-11 09:40:37 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
10:42:34 2025-07-11 09:42:34 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21796588 (promoted build)
10:42:49 2025-07-11 09:42:49 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21947691 (drone build)
10:42:49 2025-07-11 09:42:49 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:42:49 2025-07-11 09:42:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
10:42:52 2025-07-11 09:42:52 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
10:44:24 2025-07-11 09:44:24 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:44:24 2025-07-11 09:44:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
10:44:26 2025-07-11 09:44:26 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
10:44:33 2025-07-11 09:44:33 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24241970 (drone build)
10:44:47 2025-07-11 09:44:47 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24242417 (drone build)
10:45:30 2025-07-11 09:45:30 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk\23569006 (promoted build)
10:45:30 2025-07-11 09:45:30 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:45:30 2025-07-11 09:45:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
10:45:32 2025-07-11 09:45:32 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: Deletion plan: 0 orphan builds, 4 regular builds (retention)
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: Deleting 4 builds (4 regular, 0 orphan)
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: Phase 2: Deleting 4 builds based on retention policy
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24380483
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24381045
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24381806
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24382010
10:45:40 2025-07-11 09:45:40 elipy2 [INFO]: All planned deletions completed successfully
10:45:41 2025-07-11 09:45:41 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
10:45:42 2025-07-11 09:45:41 elipy2 [INFO]: Nothing to delete! builds available 0 <= 40 maximum builds
10:45:42 2025-07-11 09:45:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ZIPs
10:45:43 2025-07-11 09:45:43 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
10:46:11 2025-07-11 09:46:11 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
10:46:11 2025-07-11 09:46:11 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
10:46:11 2025-07-11 09:46:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
10:46:12 2025-07-11 09:46:12 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
10:46:36 2025-07-11 09:46:36 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
10:46:36 2025-07-11 09:46:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23960235
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23962304
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23963269
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23966316
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23968940
10:46:37 2025-07-11 09:46:37 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24023699
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24024280
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24037412
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24401195
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24401749
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24403388
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found 11 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan: 11 total builds found (11 direct, 0 in subdirs)
10:46:38 2025-07-11 09:46:38 elipy2 [INFO]: Found 11 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
10:47:01 2025-07-11 09:47:01 elipy2 [INFO]: Nothing to delete! builds available 4 <= 40 maximum builds
10:47:01 2025-07-11 09:47:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
10:47:19 2025-07-11 09:47:19 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23836410 (drone build)
10:47:23 2025-07-11 09:47:23 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\23681229 (promoted build)
10:47:32 2025-07-11 09:47:32 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23853895 (drone build)
10:47:32 2025-07-11 09:47:32 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:47:32 2025-07-11 09:47:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
10:47:36 2025-07-11 09:47:36 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24231509 (drone build)
10:47:48 2025-07-11 09:47:48 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24234018 (drone build)
10:47:48 2025-07-11 09:47:48 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:47:48 2025-07-11 09:47:48 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
10:47:50 2025-07-11 09:47:50 elipy2 [INFO]: Found 37 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
10:48:07 2025-07-11 09:48:07 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
10:48:07 2025-07-11 09:48:07 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
10:48:07 2025-07-11 09:48:07 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
10:48:07 2025-07-11 09:48:07 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage\24285257
10:48:08 2025-07-11 09:48:08 elipy2 [INFO]: All planned deletions completed successfully
10:48:10 2025-07-11 09:48:10 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
10:50:01 2025-07-11 09:50:01 elipy2 [INFO]: Nothing to delete! builds available 11 <= 40 maximum builds
10:50:01 2025-07-11 09:50:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
10:50:02 2025-07-11 09:50:02 elipy2 [INFO]: Found 24 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
10:51:56 2025-07-11 09:51:56 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24179982 (promoted build)
10:52:07 2025-07-11 09:52:07 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24222845 (drone build)
10:52:18 2025-07-11 09:52:18 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24223794 (drone build)
10:52:31 2025-07-11 09:52:31 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
10:52:31 2025-07-11 09:52:31 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
10:52:31 2025-07-11 09:52:31 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
10:52:31 2025-07-11 09:52:31 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24368709
10:52:31 2025-07-11 09:52:31 elipy2 [INFO]: All planned deletions completed successfully
10:53:11 2025-07-11 09:53:11 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24120608 (drone build)
10:53:22 2025-07-11 09:53:22 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24121042 (drone build)
10:53:22 2025-07-11 09:53:22 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:53:22 2025-07-11 09:53:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
10:53:24 2025-07-11 09:53:24 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:53:24 2025-07-11 09:53:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
10:55:00 2025-07-11 09:55:00 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23138880 (drone build)
10:55:07 2025-07-11 09:55:07 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23141586 (drone build)
10:55:07 2025-07-11 09:55:07 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:55:07 2025-07-11 09:55:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
10:55:35 2025-07-11 09:55:35 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
10:55:35 2025-07-11 09:55:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
10:55:37 2025-07-11 09:55:37 elipy2 [INFO]: Nothing to delete! builds available 24 <= 40 maximum builds
10:55:37 2025-07-11 09:55:37 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
10:56:05 2025-07-11 09:56:05 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23180978 (drone build)
10:56:08 2025-07-11 09:56:08 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23318395 (drone build)
10:56:08 2025-07-11 09:56:08 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:56:08 2025-07-11 09:56:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
10:56:15 2025-07-11 09:56:15 elipy2 [INFO]: Nothing to delete! builds available 37 <= 40 maximum builds
10:56:15 2025-07-11 09:56:15 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
10:56:37 2025-07-11 09:56:37 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24145276 (promoted build)
10:56:39 2025-07-11 09:56:39 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24253749 (drone build)
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24254058 (drone build)
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Disk-based discovery found 12 branches: ['0.1.0', '0.15.0', '0.15.1', '********', '0.15.2', '0.16.0', '0.16.1', '0.16.2', '0.17.0', '0.17.1', '*******', '2.0.0']
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
10:56:42 2025-07-11 09:56:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Disk-based discovery found 29 branches: ['CH1-SP-content-dev', 'CH1-SP-stage', 'CH1-code-dev', 'CH1-code-dev-asan', 'CH1-code-dev-sanitizers', 'CH1-content-dev', 'CH1-content-dev-cache', 'CH1-content-dev-metrics', 'CH1-marketing-dev', 'CH1-playtest', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'media-team', 'task1', 'task2', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-sanitizers', 'trunk-code-dev-skybuild', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-playtest', 'trunk-to-dev-na']
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 16 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 51 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
10:56:43 2025-07-11 09:56:43 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
10:58:38 2025-07-11 09:58:38 elipy2 [INFO]: Nothing to delete! builds available 5 <= 50 maximum builds
10:58:38 2025-07-11 09:58:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
10:58:39 2025-07-11 09:58:39 elipy2 [INFO]: Found 21 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
10:58:57 2025-07-11 09:58:57 elipy2 [INFO]: Nothing to delete! builds available 6 <= 50 maximum builds
10:58:57 2025-07-11 09:58:57 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
10:58:58 2025-07-11 09:58:58 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
10:59:22 2025-07-11 09:59:22 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
10:59:22 2025-07-11 09:59:22 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
10:59:23 2025-07-11 09:59:23 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
11:02:02 2025-07-11 10:02:02 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
11:02:02 2025-07-11 10:02:02 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
11:02:03 2025-07-11 10:02:03 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
11:02:03 2025-07-11 10:02:03 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
11:02:04 2025-07-11 10:02:04 elipy2 [INFO]: Found 51 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
11:02:04 2025-07-11 10:02:04 elipy2 [INFO]: Found 29 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
11:02:50 2025-07-11 10:02:50 elipy2 [INFO]: Nothing to delete! builds available 16 <= 50 maximum builds
11:02:50 2025-07-11 10:02:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
11:02:51 2025-07-11 10:02:51 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
11:03:12 2025-07-11 10:03:12 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
11:03:12 2025-07-11 10:03:12 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
11:03:14 2025-07-11 10:03:14 elipy2 [INFO]: Found 20 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
11:03:18 2025-07-11 10:03:18 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
11:03:18 2025-07-11 10:03:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
11:03:19 2025-07-11 10:03:19 elipy2 [INFO]: Found 22 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
11:05:53 2025-07-11 10:05:53 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
11:05:54 2025-07-11 10:05:53 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
11:05:55 2025-07-11 10:05:55 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
11:06:18 2025-07-11 10:06:18 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
11:06:18 2025-07-11 10:06:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
11:06:20 2025-07-11 10:06:20 elipy2 [INFO]: Found 52 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
11:06:51 2025-07-11 10:06:51 elipy2 [INFO]: Nothing to delete! builds available 21 <= 50 maximum builds
11:06:51 2025-07-11 10:06:51 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
11:06:52 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24003088
11:06:52 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24004119
11:06:52 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24010679
11:06:52 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013106
11:06:52 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013423
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24015920
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24019386
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24021916
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24023774
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24025232
11:06:53 2025-07-11 10:06:52 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24027941
11:06:53 2025-07-11 10:06:53 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24029263
11:06:53 2025-07-11 10:06:53 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24031339
11:06:53 2025-07-11 10:06:53 elipy2 [INFO]: Found 13 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
11:06:53 2025-07-11 10:06:53 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers: 13 total builds found (13 direct, 0 in subdirs)
11:06:53 2025-07-11 10:06:53 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
11:07:34 2025-07-11 10:07:34 elipy2 [INFO]: Nothing to delete! builds available 12 <= 50 maximum builds
11:07:34 2025-07-11 10:07:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
11:07:35 2025-07-11 10:07:35 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
11:10:58 2025-07-11 10:10:58 elipy2 [INFO]: Nothing to delete! builds available 20 <= 50 maximum builds
11:10:58 2025-07-11 10:10:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
11:11:00 2025-07-11 10:11:00 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
11:11:49 2025-07-11 10:11:49 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
11:11:49 2025-07-11 10:11:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
11:11:49 2025-07-11 10:11:49 elipy2 [INFO]: Nothing to delete! builds available 22 <= 50 maximum builds
11:11:49 2025-07-11 10:11:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
11:11:50 2025-07-11 10:11:50 elipy2 [INFO]: Found 26 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
11:11:51 2025-07-11 10:11:51 elipy2 [INFO]: Found 11 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
11:13:18 2025-07-11 10:13:18 elipy2 [INFO]: Nothing to delete! builds available 29 <= 50 maximum builds
11:13:18 2025-07-11 10:13:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23846862
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23904765
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23916815
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23929926
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23931126
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23950869
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23960229
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23962304
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23963269
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23966316
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23968940
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24023699
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24024280
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24037412
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found 14 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers: 14 total builds found (14 direct, 0 in subdirs)
11:13:19 2025-07-11 10:13:19 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
11:14:16 2025-07-11 10:14:16 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
11:14:16 2025-07-11 10:14:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
11:14:18 2025-07-11 10:14:18 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
11:14:41 2025-07-11 10:14:41 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
11:14:41 2025-07-11 10:14:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
11:14:42 2025-07-11 10:14:42 elipy2 [INFO]: Found 48 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
11:15:46 2025-07-11 10:15:46 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
11:15:46 2025-07-11 10:15:46 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
11:15:52 2025-07-11 10:15:52 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
11:15:52 2025-07-11 10:15:52 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
11:15:59 2025-07-11 10:15:59 elipy2 [INFO]: Nothing to delete! builds available 11 <= 50 maximum builds
11:15:59 2025-07-11 10:15:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
11:16:03 2025-07-11 10:16:03 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
11:16:03 2025-07-11 10:16:03 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
11:16:18 2025-07-11 10:16:18 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
11:16:18 2025-07-11 10:16:18 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
11:16:18 2025-07-11 10:16:18 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
11:16:18 2025-07-11 10:16:18 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev\24356156
11:16:19 2025-07-11 10:16:19 elipy2 [INFO]: All planned deletions completed successfully
11:17:20 2025-07-11 10:17:20 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
11:17:20 2025-07-11 10:17:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
11:18:50 2025-07-11 10:18:50 elipy2 [INFO]: Nothing to delete! builds available 26 <= 50 maximum builds
11:18:50 2025-07-11 10:18:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
11:18:52 2025-07-11 10:18:52 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
11:18:52 2025-07-11 10:18:52 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
11:18:52 2025-07-11 10:18:52 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
11:18:52 2025-07-11 10:18:52 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage\24262360
11:18:52 2025-07-11 10:18:52 elipy2 [INFO]: All planned deletions completed successfully
11:20:27 2025-07-11 10:20:26 elipy2 [INFO]: Deletion plan: 0 orphan builds, 2 regular builds (retention)
11:20:27 2025-07-11 10:20:27 elipy2 [INFO]: Deleting 2 builds (2 regular, 0 orphan)
11:20:27 2025-07-11 10:20:27 elipy2 [INFO]: Phase 2: Deleting 2 builds based on retention policy
11:20:27 2025-07-11 10:20:27 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24379482
11:20:27 2025-07-11 10:20:27 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24379677
11:20:27 2025-07-11 10:20:27 elipy2 [INFO]: All planned deletions completed successfully
11:21:08 2025-07-11 10:21:08 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
11:21:08 2025-07-11 10:21:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
11:21:34 2025-07-11 10:21:34 elipy2 [INFO]: Nothing to delete! builds available 48 <= 50 maximum builds
11:21:34 2025-07-11 10:21:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
11:21:35 2025-07-11 10:21:35 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
11:21:35 2025-07-11 10:21:35 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
11:21:35 2025-07-11 10:21:35 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
11:22:23 2025-07-11 10:22:23 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
11:22:23 2025-07-11 10:22:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
11:22:24 2025-07-11 10:22:24 elipy2 [WARNING]: Path does not exist, skipping branch discovery: \\filer.dice.ad.ea.com\builds\Battlefield\tnt_local
11:22:24 2025-07-11 10:22:24 elipy2 [INFO]: Skipping azure_fileshare_path_retention files
11:22:24 2025-07-11 10:22:24 elipy2 [WARNING]: Skipping Avalanche db deletion.
11:22:24 2025-07-11 10:22:24 elipy2 [WARNING]: Skipping symstore cleanup.
