package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibIntegration
import com.ea.project.GetMasterFile
import com.ea.project.all.All
import javaposse.jobdsl.dsl.jobs.FreeStyleJob

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def integrate_branches = masterSettings.integrate_branches
    def copy_branches = masterSettings.copy_branches
    def feature_integrations = masterSettings.feature_integrations

    out.println('   Processing integration branches...')
    integrate_branches.each { current_branch, branch_info ->
        out.println("       Processing branch: $current_branch")
        // Integration jobs
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = branch_info.project
        }
        def source_project = branch_info.source_project ?: project
        def target_project = branch_info.target_project ?: project
        def data_upgrade = branch_info.data_upgrade ?: false
        branch_info += [dataset: target_project.dataset]
        def freestyle_jobs = []

        def direction = null
        switch (branch_info.parent_to_child) {
            case null: direction = 'integrate-to'; break
            case true: direction = 'merge-down'; break
            case false: direction = 'integrate-up'; break
        }
        if (branch_info.copy_integrate_compile) {
            direction = 'copy-integrate-to'
        } else if (branch_info.integrate_upgrade_one_stream) {
            direction = 'integrate-upgrade-to'
        }

        // Integration jobs to task branches, using verified changelists
        if (branch_info.verified_integration == true) {
            def pipeline_script = 'src/scripts/schedulers/all/verified_integration_scheduler.groovy'
            def pipeline_name = branch_info.source_branch + '.' + direction + '.' + branch_info.target_branch + '.start'
            if (data_upgrade == true) {
                pipeline_name = "${branch_info.source_branch}.${direction}.upgrade.${branch_info.target_branch}.start"
                pipeline_script = 'src/scripts/schedulers/all/verified_integration_scheduler_upgrade.groovy'
                if (branch_info.create_ref_job == true) {
                    def set_integration_changelist_job = pipelineJob(branch_info.integration_reference_job) {
                        definition {
                            cps {
                                script(readFileFromWorkspace('src/scripts/schedulers/all/integration_changelist_scheduler.groovy'))
                                sandbox(true)
                            }
                        }
                    }
                    LibIntegration.set_integration_changelist_job(set_integration_changelist_job, branch_info, pipeline_name)
                }
            }
            def verified_integration_start = pipelineJob(pipeline_name) {
                definition {
                    cps {
                        script(readFileFromWorkspace(pipeline_script))
                        sandbox(true)
                    }
                }
            }
            LibIntegration.verified_integration_start(verified_integration_start, branch_info, project)
        }

        def code_changelist = '${code_changelist}'
        def data_changelist = '${data_changelist}'
        if (branch_info.verified_integration != true) {
            code_changelist = ''
            data_changelist = ''
        }
        def job_name_integrate_data = branch_info.source_branch + '.data.' + direction + '.' + branch_info.target_branch

        if (data_upgrade == true) {
            job_name_integrate_data = branch_info.source_branch + '.data.' + direction + '.upgrade.' + branch_info.target_branch
        }
        if (branch_info.code == true) {
            def integrate_code = job(branch_info.source_branch + '.code.' + direction + '.' + branch_info.target_branch) {}
            freestyle_jobs.add(integrate_code)
            LibIntegration.integrate_code(integrate_code, source_project, target_project, branch_info)
            LibScm.integrate_code(integrate_code, source_project, target_project, branch_info, '${code_changelist}')
            LibIntegration.cleanUp(integrate_code, target_project, branch_info)
            LibIntegration.slackSend(integrate_code, branch_info, target_project)

            if (branch_info.data == true) {
                def integrate_data = job(job_name_integrate_data) {}
                freestyle_jobs.add(integrate_data)
                if (data_upgrade == true) {
                    LibIntegration.data_upgrade(integrate_data, target_project, branch_info)
                    LibScm.integrate_data_upgrade(integrate_data, source_project, target_project, branch_info.preview_project, branch_info, data_changelist, code_changelist)
                } else {
                    LibIntegration.integrate_data(integrate_data, source_project, target_project, branch_info)
                    LibScm.integrate_data_default(integrate_data, source_project, target_project, branch_info, '${data_changelist}')
                }
                LibIntegration.cleanUp(integrate_data, target_project, branch_info)
                LibIntegration.slackSend(integrate_data, branch_info, target_project)
            }
        } else if (branch_info.code == false && branch_info.data == true) {
            def integrate_data_only = job(job_name_integrate_data) {}
            freestyle_jobs.add(integrate_data_only)
            if (data_upgrade == true) {
                LibIntegration.data_upgrade(integrate_data_only, target_project, branch_info)
                LibScm.integrate_data_upgrade(integrate_data_only, source_project, target_project, branch_info.preview_project, branch_info, data_changelist, code_changelist)
            } else {
                LibIntegration.integrate_data(integrate_data_only, source_project, target_project, branch_info)
                if (branch_info.data_only_source_branch == true) {
                    LibScm.integrate_data_default(integrate_data_only, source_project, target_project, branch_info, '${data_changelist}')
                } else if (branch_info.custom_code_branch == true) {
                    LibScm.integrate_data_custom(integrate_data_only, source_project, target_project, branch_info, '${data_changelist}')
                } else {
                    LibScm.integrate_data_no_code(integrate_data_only, source_project, target_project, branch_info, '${data_changelist}')
                }
            }
            LibIntegration.cleanUp(integrate_data_only, target_project, branch_info)
            LibIntegration.slackSend(integrate_data_only, branch_info, target_project)
        } else if (branch_info.smoke_integration == true) {
            def branch_info_sync = [
                code_branch: branch_info.target_branch,
                code_folder: branch_info.target_folder,
                data_branch: branch_info.target_branch,
                data_folder: branch_info.target_folder,
                dataset    : target_project.dataset,
            ]
            def future_smoke_copy_up = job(branch_info.target_branch + '.future-smoke.copy-up-from.' + branch_info.source_branch)
            freestyle_jobs.add(future_smoke_copy_up)
            LibIntegration.future_smoke_copy_up(future_smoke_copy_up, project, branch_info)
            LibScm.sync_code_and_data(future_smoke_copy_up, project, branch_info_sync)

            LibIntegration.cleanUp(future_smoke_copy_up, target_project, branch_info)
            if (branch_info.offsite_drone_builds == true) {
                LibJobDsl.curl_drone_builds(future_smoke_copy_up, branch_info)
            }
            LibIntegration.slackSend(future_smoke_copy_up, branch_info, project)
        } else if (branch_info.copy_integrate_compile) {
            FreeStyleJob copyIntegrateCompile = job(branch_info.source_branch + '.code.copy-integrate-to.' + branch_info.target_branch as String) {}
            freestyle_jobs.add(copyIntegrateCompile)
            LibIntegration.copyIntegrateCompile(copyIntegrateCompile, target_project, branch_info as Map)
            LibScm.integrate_code(copyIntegrateCompile, source_project, target_project, branch_info, '${code_changelist}')

            LibIntegration.cleanUp(copyIntegrateCompile, target_project, branch_info)
            LibIntegration.slackSend(copyIntegrateCompile, branch_info, target_project)
        } else if (branch_info.integrate_compile_upgrade_cook) {
            Map branchInfoSync = [
                code_branch: branch_info.target_branch,
                code_folder: branch_info.target_folder,
                data_branch: branch_info.data_branch,
                data_folder: branch_info.data_folder,
            ]
            branch_info += [dataset: branch_info.data_project.dataset]
            FreeStyleJob integrateCompileUpgradeCook = job(branch_info.source_branch + '.code.compile-upgrade-cook.integrate-to.' + branch_info.target_branch as String) {}
            freestyle_jobs.add(integrateCompileUpgradeCook)
            LibIntegration.integrateCompileUpgradeCook(integrateCompileUpgradeCook, branch_info.target_project, branch_info.data_project, branch_info as Map)
            LibScm.sync_code_and_data_different_projects(integrateCompileUpgradeCook, target_project, branch_info.data_project, branchInfoSync)

            LibIntegration.cleanUp(integrateCompileUpgradeCook, target_project, branch_info)
            LibIntegration.slackSend(integrateCompileUpgradeCook, branch_info, target_project)
        } else if (branch_info.p4_copy_data_upgrade) {
            Map branchInfoSync = [
                code_branch: branch_info.target_branch,
                code_folder: branch_info.target_folder,
            ]
            branch_info += [dataset: branch_info.target_project.dataset]
            FreeStyleJob p4CopyDataUpgrade = job(branch_info.source_branch + '.copy-code-to.upgrade-data.' + branch_info.target_branch as String) {}
            freestyle_jobs.add(p4CopyDataUpgrade)
            LibIntegration.p4CopyDataUpgrade(p4CopyDataUpgrade, branch_info.target_project, branch_info as Map)
            LibScm.sync_code(p4CopyDataUpgrade, target_project, branchInfoSync)

            LibIntegration.cleanUp(p4CopyDataUpgrade, target_project, branch_info)
            LibIntegration.slackSend(p4CopyDataUpgrade, branch_info, target_project)
        } else if (branch_info.integrate_upgrade_one_stream) {
            Map branchInfoSync = branch_info + [
                code_branch: branch_info.target_branch,
                code_folder: branch_info.target_folder,
            ]
            FreeStyleJob integrateUpgradeOneStream = job(branch_info.source_branch + '.integrate-upgrade-to.' + branch_info.target_branch as String) {}
            freestyle_jobs.add(integrateUpgradeOneStream)
            LibIntegration.integrateUpgradeOneStream(integrateUpgradeOneStream, branch_info.source_project, branch_info.target_project, branch_info as Map)
            if (branch_info.branch_guardian) {
                branchInfoSync += [
                    data_branch   : 'branchguardian',
                    data_folder   : 'admin',
                    p4_data_client: project.p4_code_client + '-branchguardianrules',
                    p4_data_root  : '//bf',
                ]
                if (branch_info.p4_code_creds) {
                    branchInfoSync += [p4_data_creds: branch_info.p4_code_creds]
                }
                LibScm.sync_code_and_data_different_projects(integrateUpgradeOneStream, target_project, target_project, branchInfoSync)
            } else {
                LibScm.sync_code(integrateUpgradeOneStream, target_project, branchInfoSync)
            }
            LibIntegration.cleanUp(integrateUpgradeOneStream, target_project, branch_info)
            LibIntegration.slackSend(integrateUpgradeOneStream, branch_info, target_project)
        }

        if (branch_info.get_integration_info == true) {
            def autotest_to_integration = job(branch_info.source_branch + '.autotest-to-integration.code') {}
            freestyle_jobs.add(autotest_to_integration)
            LibIntegration.autotest_to_integration_code_get(autotest_to_integration, branch_info)
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, '', branch_info.freestyle_job_trigger_matrix)
    }

    out.println('   Processing copy branches...')
    copy_branches.each { current_branch, branch_info ->
        out.println("       Processing branch: $current_branch")
        // Copy jobs
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = branch_info.project
        }
        branch_info += [dataset: project.dataset]
        def freestyle_jobs = []

        if (branch_info.code == true) {
            def copy_to_code = job(branch_info.source_branch + '.code.copy-to.' + branch_info.target_branch) {}
            freestyle_jobs.add(copy_to_code)
            LibIntegration.copy_to_code(copy_to_code, project, branch_info)
            LibScm.integrate_code(copy_to_code, project, project, branch_info, '${code_changelist}')
            LibIntegration.cleanUp(copy_to_code, project, branch_info)
            LibIntegration.slackSend(copy_to_code, branch_info, project)

            if (branch_info.data == true) {
                def copy_to_data = job(branch_info.source_branch + '.data.copy-to.' + branch_info.target_branch) {}
                freestyle_jobs.add(copy_to_data)
                LibIntegration.copy_to_data(copy_to_data, project, branch_info)
                LibScm.integrate_data_default(copy_to_data, project, project, branch_info, '${data_changelist}')
                LibIntegration.cleanUp(copy_to_data, project, branch_info)
                LibIntegration.slackSend(copy_to_data, branch_info, project)
            }
        }

        if (branch_info.code == false && branch_info.data == true) {
            def copy_to_data_only = job(branch_info.source_branch + '.data.copy-to.' + branch_info.target_branch) {}
            freestyle_jobs.add(copy_to_data_only)
            LibIntegration.copy_to_data(copy_to_data_only, project, branch_info)
            if (branch_info.data_only_source_branch == true) {
                LibScm.integrate_data_default(copy_to_data_only, project, project, branch_info, '${data_changelist}')
            } else if (branch_info.custom_code_branch == true) {
                LibScm.integrate_data_custom(copy_to_data_only, project, project, branch_info, '${data_changelist}')
            } else {
                LibScm.integrate_data_no_code(copy_to_data_only, project, project, branch_info, '${data_changelist}')
            }
            LibIntegration.cleanUp(copy_to_data_only, project, branch_info)
            LibIntegration.slackSend(copy_to_data_only, branch_info, project)
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, '', branch_info.freestyle_job_trigger_matrix)
    }

    out.println('   Processing feature branches...')
    feature_integrations.each { current_branch, branch_info ->
        out.println("       Processing branch: $current_branch")
        // Integration jobs
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = branch_info.project
        }
        branch_info += [dataset: project.dataset]

        def source_project = branch_info.source_project ?: project
        def target_project = branch_info.target_project ?: project
        def cherrypick = branch_info.cherrypick ?: false

        if (branch_info.data_upgrader_validator == true) {
            def data_upgrader_validator_start = pipelineJob('data-upgrader-validator.for.' + branch_info.source_branch + '.to.' + branch_info.target_branch + '.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/upgrader_validator_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibIntegration.data_upgrader_validator_start(data_upgrader_validator_start, branch_info)

            def data_upgrader_validator_job = job('data-upgrader-validator.for.' + branch_info.source_branch + '.to.' + branch_info.target_branch) {}
            LibIntegration.data_upgrader_validator_job(data_upgrader_validator_job, branch_info)
            LibScm.data_upgrader_validator_default(data_upgrader_validator_job, branch_info, '${code_changelist}')
            LibIntegration.cleanUp(data_upgrader_validator_job, target_project, branch_info)
        } else if (cherrypick == true) {
            branch_info += [dataset: target_project.dataset]

            def cherrypick_job_name = branch_info.source_branch + '_to_' + branch_info.target_branch + '.cherrypick.code.start'
            if (branch_info.data_stream == true) {
                cherrypick_job_name = branch_info.source_branch + '_to_' + branch_info.target_branch + '.cherrypick.data.start'
            }

            def cherrypick_start = pipelineJob(cherrypick_job_name) {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/cherrypick_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibIntegration.cherrypick_start(cherrypick_start, target_project, branch_info)

            if (branch_info.data_stream == true) {
                def cherrypick_job_data = job(branch_info.source_branch + '.cherrypick_integration.data.' + branch_info.target_branch) {}
                LibIntegration.cherrypick(cherrypick_job_data, target_project, branch_info)
                LibScm.integrate_data_cherrypick(cherrypick_job_data, source_project, target_project, branch_info)
                LibIntegration.cleanUp(cherrypick_job_data, target_project, branch_info)
            } else {
                def cherrypick_job = job(branch_info.source_branch + '.cherrypick_integration.' + branch_info.target_branch) {}
                LibIntegration.cherrypick(cherrypick_job, target_project, branch_info)
                LibScm.integrate_code_cherrypick(cherrypick_job, source_project, target_project, branch_info)
                LibIntegration.cleanUp(cherrypick_job, target_project, branch_info)
            }
        } else if (branch_info.cherrypick_v2) {
            if (branch_info.code) {
                def cherrypickJobCode = job("code.cherrypick-to.${branch_info.code_branch}") {}
                LibIntegration.cherrypickCode(cherrypickJobCode, target_project, branch_info)
                LibScm.sync_code(cherrypickJobCode, target_project, branch_info, '${code_changelist}')
                LibIntegration.cleanUp(cherrypickJobCode, target_project, branch_info)
                LibIntegration.slackSend(cherrypickJobCode, branch_info, project)
            }
            if (branch_info.data) {
                def cherrypickJobData = job("data.cherrypick-to.${branch_info.data_branch}") {}
                LibIntegration.cherrypickData(cherrypickJobData, target_project, branch_info)
                LibScm.sync_code_and_data(cherrypickJobData, target_project, branch_info, '${data_changelist}')
                LibIntegration.cleanUp(cherrypickJobData, target_project, branch_info)
                LibIntegration.slackSend(cherrypickJobData, branch_info, project)
            }
        }
    }
}
