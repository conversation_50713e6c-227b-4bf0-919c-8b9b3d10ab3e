# ELIPY Config

## Info

This document will not cover all the details of the ELIPY config file, just those that are referenced by this repository. For more details, please see the [ELIPY config docs](https://gitlab.ea.com/dre-cobra/elipy/elipy2/-/blob/master/ELIPY_CONFIG.md).

Each item in this document will contain the following:

- Title/Name
- Type information
- If it is required or not
  - Some settings will be required depending on what scripts you are running or what settings you have passed in.
- Example configuration
- Description

## Location `default`

A location in the ELIPY config file is a key-value-part that you can find at the root for the file. The `default` value is mandatory but you can create as many locations as you want.

Each additional location you create, you can override the settings found in the `default` location.

### skip_increment_client_version

- *type*: bool
- *required*: False
- *example*:

```yaml
default:
  skip_increment_client_version: true
```

- *description*: If we should skip incrementing the client version when building code.

### enable_threshold_clean

- *type*: bool
- *required*: False
- *example*:

```yaml
default:
  enable_threshold_clean: true
```

- *description*: Should we perform frostbite threshold cleaning process or not on `codebuild.py` or `databuild.py` builds

### recompression_cache

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
  recompression_cache:
    xb1: "kraken-01.dre.ea.com"
    ps4: "kraken-02.dre.ea.com"
```

- *description*: What Avalanche instances, per platform, should be used for recompression data caches

### avalanche_state_host

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
  avalanche_state_host:
    xb1: "statehost-01.dre.ea.com"
    ps4: "statehost-02.dre.ea.com"
```

- *description*: What Avalanche instances, per platform, should be used for state hosts

### snowcache_host

- *type*: dict
- *required*: True
- *example*:

```yaml
default:
  snowcache_host:
    ps5: 'snowcache-01.dre.ea.com'
    xb1: 'snowcache-02.dre.ea.com'
```

- *description*: What Avalanche instances, per platform, should be used for using as a SnowCache host

### retention_categories

- *type*: list[dict]
- *required*: False
- *example*:

```yaml
default:
  retention_categories:
    code:
      - 'default': 10
      - 'my_stream_name': 15
    frosty\BattlefieldGame:
      - 'default': 10
      - 'my_stream_name': 15
```

- *description*: Used by `deleter.py` to manage the retention of artifacts that we have metadata stored in Bilbo. Effectively, the dictionary keys help to form the path of the artifacts that we want to keep. The `default` key is special and lets you set a default retention for all branches if they are not specified.

### shift_retention

- *type*: int
- *required*: False
- *example*:

```yaml
default:
  shift_retention: 57
```

- *description*: Determines how many builds under the `shift_submission_path` should be retained when performing clean-up.

### path_retention

- *type*: list[dict]
- *required*: False
- *example*:

```yaml
default:
  path_retention:
    - \\my_studio_share.dre.ea.com\\builds\Project\publishedbuilds: 10
    - \\my_studio_share.dre.ea.com\\builds\Project\offsite\my_stream_name: 15
    - \\my_studio_share.dre.ea.com\\builds\Project\crashdumps\pipeline_crashdumps: 20
```

- *description*: Used to determine how many individual items should be keeps in the key path when performing clean-up.

### release_candidate_retention

- *type*: int
- *required*: False
- *example*:

```yaml
default:
  release_candidate_retention: 10
```

- *description*: How many release candidate builds we should keep when performing clean-up. These builds are still included in the over retention count.

### skip_frosty_game_config_flags

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  skip_frosty_game_config_flags: "true"
```

- *description*: Should we skip adding game config flags to the FrostyIsoTool invocation.

### elsa_patch

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  elsa_patch: "true"
```

- *description*: Should we pass the `ELSA_PATCH=true` arg to FrostyIsoTool when invoking

### fetch_xb_basepackage

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  fetch_xb_basepackage: "true"
```

- *description*: Should we fetch the Xbox baseline package when creating patches.

### spin_s3_bucket

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  spin_s3_bucket: "ups-franchise-project"
```

- *description*: What S3 bucket should we attempt to upload Spin builds.

### vault_destination

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  vault_destination: "\\\\my_studio_share.dre.ea.com\\builds\\vault"
```

- *description*: Where should we store vaulted build.

### vault_symstore

- *type*: bool
- *required*: False
- *example*:

```yaml
default:
  vault_symstore: "true"
```

- *description*: Should we store symbols in the `vault_destination`

### vault_verification_config_path

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  vault_verification_config_path: "vault_verification_config_project.yml"
```

- *description*: A file that describes the requirements of a vaulted build. This includes the number of files/items and specific files/folders for a given path for each platform.

### p4_package_server

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  p4_package_server: "project-perforceproxy.dre.ea.com:1680"
```

- *description*: What package server should we attempt to authenticate against.

### metadata_files

- *type*: list[string]
- *required*: False
- *example*:

```yaml
default:
  add_metadata_files:
    - "AutoIntegrate.json"
```

- *description*: Adds additional metadata to the build Bilbo entry.

### gamescripts_script_path

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  gamescripts_script_path: "Code\\FIFAGame\\fbcli\\buildgamescripts.py"
```

- *description*: Utility script path for generating gamescripts.

### unified_code_data_stream_project

- *type*: string
- *required*: False
- *example*:

```yaml
default:
  unified_code_data_stream_project: "false"
```

- *description*: Projects where code and data exist .
