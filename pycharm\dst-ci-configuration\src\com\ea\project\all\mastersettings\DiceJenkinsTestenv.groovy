package com.ea.project.all.mastersettings

import com.ea.project.all.All
import com.ea.project.kin.Kingston

class DiceJenkinsTestenv {
    static Class project = All
    static Map branches = [
        'lab.kin-dev': [project: Kingston, code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev'],
    ]
    static Map preflight_branches = [
        'lab.kin-dev': [project: Kingston, code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev'],
    ]
    static Map autotest_branches = [
        'kin-dev'           : [
            project               : TestProject,
            code_folder           : 'dev',
            code_branch           : 'kin-dev',
            data_folder           : 'dev',
            data_branch           : 'kin-dev',
            statebuild_autotest   : true,
            enable_lkg_p4_counters: false,
            job_label_statebuild  : 'statebuild',
        ],
        'kin-dev-unverified': [
            project                   : TestProject,
            code_folder               : 'dev',
            code_branch               : 'kin-dev-unverified',
            data_folder               : 'dev',
            data_branch               : 'kin-dev-unverified',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            job_label_statebuild      : 'test',
            enable_lkg_p4_counters    : false,
        ],
        'lab.kin-dev'       : [
            project               : TestProject,
            code_folder           : 'dev',
            code_branch           : 'kin-dev',
            data_folder           : 'dev',
            data_branch           : 'kin-dev',
            statebuild_autotest   : true,
            enable_lkg_p4_counters: false,
            job_label_statebuild  : 'statebuild_eala',
            poolbuild_label       : 'poolbuild_eala',
        ],
    ]
    static Map integrate_branches = [
        'kin-stage_to_kin-dev': [
            code                        : true,
            cook_before_submit          : true,
            data                        : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            no_safe_resolve             : true,
            no_submit                   : false,
            parent_to_child             : true,
            source_branch               : 'kin-stage',
            source_folder               : 'stage',
            source_project              : Kingston,
            target_branch               : 'kin-dev',
            target_folder               : 'dev',
            target_project              : Kingston,
            trigger_type_integrate      : 'scm',
            workspace_root              : project.workspace_root,
            freestyle_job_trigger_matrix: [],
        ]
    ]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'lab.kin-dev': [
            project                           : TestProject,
            code_folder                       : 'dev',
            code_branch                       : 'kin-dev',
            data_folder                       : 'dev',
            data_branch                       : 'kin-dev',
            include_vault                     : true,
            include_register_release_candidate: true,
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [
        ORPHANED_TRIGGER                 : 'H 6 * * *',
        ORPHANED_PROJECT_SHORT_NAME      : 'kin',
        JOB_MONITORING_TRIGGER           : 'H 6 * * *',
        JOB_MONITORING_PROJECT_SHORT_NAME: 'kin',
        IS_PRODUCTION                    : false,
    ]

    private static class TestProject extends Kingston {
        static String autotest_matrix = 'TestAutotestMatrix'
    }
}
