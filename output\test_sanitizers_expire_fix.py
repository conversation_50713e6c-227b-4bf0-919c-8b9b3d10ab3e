#!/usr/bin/env python3
"""
Test script to verify the sanitizers fix in expire.py

This script tests the new sanitizer directory scanning logic to ensure:
1. Main sanitizer directories only include direct CL directories
2. Sanitizer subdirectories (asan, ubsan) are handled correctly when scanned directly
3. The helper methods correctly identify directory types
"""

import os
import sys
import tempfile
import shutil

# Add the elipy2 module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'pycharm', 'elipy2'))

from elipy2.expire import ExpireUtils


def create_test_directory_structure():
    """Create a test directory structure that mimics the sanitizers layout"""
    temp_dir = tempfile.mkdtemp()
    
    # Create main sanitizer directory with direct CL directories and subdirectories
    main_sanitizer_dir = os.path.join(temp_dir, "ch1-content-dev-sanitizers")
    os.makedirs(main_sanitizer_dir)
    
    # Create direct CL directories in main sanitizer folder
    for cl in ["24283864", "24321590", "24357207"]:
        os.makedirs(os.path.join(main_sanitizer_dir, cl))
    
    # Create asan subdirectory with CL directories
    asan_dir = os.path.join(main_sanitizer_dir, "asan")
    os.makedirs(asan_dir)
    for cl in ["24037366", "24038530", "24041017"]:
        os.makedirs(os.path.join(asan_dir, cl))
    
    # Create ubsan subdirectory with CL directories
    ubsan_dir = os.path.join(main_sanitizer_dir, "ubsan")
    os.makedirs(ubsan_dir)
    for cl in ["24309398", "24311642", "24313090"]:
        os.makedirs(os.path.join(ubsan_dir, cl))
    
    return temp_dir, main_sanitizer_dir, asan_dir, ubsan_dir


def test_main_sanitizer_directory():
    """Test scanning the main sanitizer directory"""
    print("Testing main sanitizer directory scanning...")
    
    temp_dir, main_sanitizer_dir, asan_dir, ubsan_dir = create_test_directory_structure()
    
    try:
        expire_utils = ExpireUtils()
        
        # Test main sanitizer directory - should only find direct CL directories
        builds = expire_utils._scan_sanitizers_builds(main_sanitizer_dir)
        
        print(f"Main sanitizer directory: {main_sanitizer_dir}")
        print(f"Found {len(builds)} builds:")
        for build in builds:
            print(f"  {build}")
        
        # Should find 3 direct CL directories, ignoring subdirectories
        expected_count = 3
        if len(builds) == expected_count:
            print(f"✓ PASS: Found {expected_count} builds as expected")
        else:
            print(f"✗ FAIL: Expected {expected_count} builds, found {len(builds)}")
            return False
        
        # Verify all builds are direct CL directories (not in subdirectories)
        for build in builds:
            if "asan" in build or "ubsan" in build:
                print(f"✗ FAIL: Found subdirectory build in main scan: {build}")
                return False
        
        print("✓ PASS: All builds are direct CL directories")
        return True
        
    finally:
        shutil.rmtree(temp_dir)


def test_sanitizer_subdirectory():
    """Test scanning sanitizer subdirectories"""
    print("\nTesting sanitizer subdirectory scanning...")
    
    temp_dir, main_sanitizer_dir, asan_dir, ubsan_dir = create_test_directory_structure()
    
    try:
        expire_utils = ExpireUtils()
        
        # Test asan subdirectory
        asan_builds = expire_utils._scan_sanitizers_builds(asan_dir)
        print(f"ASAN directory: {asan_dir}")
        print(f"Found {len(asan_builds)} builds:")
        for build in asan_builds:
            print(f"  {build}")
        
        if len(asan_builds) == 3:
            print("✓ PASS: ASAN subdirectory found 3 builds as expected")
        else:
            print(f"✗ FAIL: ASAN expected 3 builds, found {len(asan_builds)}")
            return False
        
        # Test ubsan subdirectory
        ubsan_builds = expire_utils._scan_sanitizers_builds(ubsan_dir)
        print(f"\nUBSAN directory: {ubsan_dir}")
        print(f"Found {len(ubsan_builds)} builds:")
        for build in ubsan_builds:
            print(f"  {build}")
        
        if len(ubsan_builds) == 3:
            print("✓ PASS: UBSAN subdirectory found 3 builds as expected")
        else:
            print(f"✗ FAIL: UBSAN expected 3 builds, found {len(ubsan_builds)}")
            return False
        
        return True
        
    finally:
        shutil.rmtree(temp_dir)


def test_helper_methods():
    """Test the helper methods for identifying directory types"""
    print("\nTesting helper methods...")
    
    expire_utils = ExpireUtils()
    
    # Test _is_main_sanitizer_directory
    test_cases = [
        (["code", "ch1-content-dev-sanitizers"], True),
        (["code", "ch1-code-dev-sanitizers"], True),
        (["code", "trunk-code-dev-sanitizers"], True),
        (["code", "ch1-content-dev-sanitizers", "asan"], False),
        (["code", "ch1-content-dev-sanitizers", "ubsan"], False),
        (["code", "regular-directory"], False),
    ]
    
    for path_parts, expected in test_cases:
        result = expire_utils._is_main_sanitizer_directory(path_parts)
        if result == expected:
            print(f"✓ PASS: _is_main_sanitizer_directory({path_parts}) = {result}")
        else:
            print(f"✗ FAIL: _is_main_sanitizer_directory({path_parts}) = {result}, expected {expected}")
            return False
    
    # Test _is_sanitizer_subdirectory
    test_cases = [
        (["code", "ch1-content-dev-sanitizers", "asan"], True),
        (["code", "ch1-content-dev-sanitizers", "ubsan"], True),
        (["code", "trunk-code-dev-sanitizers", "asan"], True),
        (["code", "ch1-content-dev-sanitizers"], False),
        (["code", "ch1-code-dev-sanitizers"], False),
        (["code", "regular-directory", "asan"], False),
    ]
    
    for path_parts, expected in test_cases:
        result = expire_utils._is_sanitizer_subdirectory(path_parts)
        if result == expected:
            print(f"✓ PASS: _is_sanitizer_subdirectory({path_parts}) = {result}")
        else:
            print(f"✗ FAIL: _is_sanitizer_subdirectory({path_parts}) = {result}, expected {expected}")
            return False
    
    return True


def main():
    """Run all tests"""
    print("Testing sanitizers fix in expire.py")
    print("=" * 50)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_main_sanitizer_directory()
    all_passed &= test_sanitizer_subdirectory()
    all_passed &= test_helper_methods()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ ALL TESTS PASSED")
        return 0
    else:
        print("✗ SOME TESTS FAILED")
        return 1


if __name__ == "__main__":
    sys.exit(main())
