{"mcp": {"servers": {"taskmanager": {"type": "stdio", "command": "npx", "args": ["-y", "@kazuph/mcp-taskmanager"], "cwd": "${input:cwd}", "env": {"REDIS_HOST": "${input:redisHostname}", "REDIS_PORT": 6379, "REDIS_PASSWORD": "${input:redisPassword}"}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@playwright/mcp"]}}}, "inputs": [{"id": "cwd", "type": "promptString", "description": "Working Directory"}, {"id": "redisHostname", "type": "promptString", "description": "Redis Hostname"}, {"id": "redisPassword", "type": "promptString", "description": "Redis Password"}]}