"""
frosty.py

The frosty script copies code from the network share, copies in data bundles or
builds data (depending on settings), packages this together into a complete game
using FrostyIsoTool and uploads this to a network share. FrostyIsoTools needs
several args to run. There's one config file inside FrostyIsoTool with some
default values, we rarely have to look at that one. Then there's another config
file in the data set, here we set which files to include in the package, the exe
main name and lots of game config settings, like title, languages and id's so the
game can successfully communicate with backend and hosting platform (origin, xb, ps).

General setup:

    - Clean up the machine by killing running processes and deleting a temporary folder.
    - Initialize objects using packages from Elipy core, to be used later.
        - One instance of data.DataUtils().
        - One instance of filer.FilerUtils().
    - Set data directory, since the Frostbite build process sometimes gets an incorrect
      default value for this.
    - Copy code binaries for pipeline (produced when building the code platform tool)
      from a network share.
    - Set licensee
        - Optional step that we run when a licensee is sent as an argument.
          We need to set this when we don't build the default licensee on a stream.
          For example when we build dev-na where the default licensee is ExampleGame.
    - Run a correction for the EA Installer signtool, since it's incorrectly configured.
    - If needed, clean the Avalanche state.
    - Handle data in one of the following two ways:
        - Import bundles produced by the data job.
        - Build data in the frosty job:
            - If not cleaning the Avalanche state, import the state
              from a network share if needed.
            - Cook the data.
            - Set Avalanche status, so we know which machine has build which platform
              using which changelist.
    - Set frosty_platform to be different to platform for linux64, since this platform
      is only partly supported by FrostyIsoTool.
    - Install tools if needed:
        - SDKs in the case of ps and xb.
            - After installing sdks we need to update the frostbite environment,
              so the correct env vars are set
        - Submission validator in the case of xb1:
            - Submission validator needs to be in the xb1 sdk folder, but is not installed
              with the sdk, so we need to take the latest version and put it in the sdk folder.
    - Copy code binaries for the platform we're building.
        - Copy for extra platforms or extra configs if specified.
    - Set some special arguments, this part is quite hard coded and not future proof.
        - These are flags to FrostyIsoTool to run in certain ways depending on if we're
          building retail builds and such.
    - Run FrostyIsoTool to package the code and data to a game build.
    - If extra configs are used, copy these to the frosty output folder.
    - If we are building for the format files, handle the builtLevels.json file
      (describing which levels that are included in the build):
        - If using previously built bundles, copy the file to the frosty output folder.
        - Otherwise, register the file in Avalanche.
    - Upload the packaged build to the network share.
    - Upload symbols to a symbol store.
        - FrostyIsoTool renames the binary for win64 games, if building retail.
        In that case we like to make sure we store the symbol, with the new name,
        so that can be found. Exception to this is when we denuvo wrap and are using
        the fake denuvo symbol setup, developed by dice (explained in codebuild section).


Examples:
    * elipy --location criterion frosty win64 files final StagingLevels
      --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
      --data-changelist 436418 --data-directory Data --use-deployed-bundles
    * elipy --location criterion frosty win64 files final StagingLevels
      --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
      --data-changelist 436418 --data-directory Data
    * elipy --location criterion frosty win64 files final StagingLevels
      --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
      --data-changelist 436418 --data-directory Data --region ww --data-clean False
      --additional-configs retail --email <EMAIL> --password "****"
      --clean-master-version-check
    * elipy --location dice frosty ps4 digital final ShippingLevels --code-branch kin-dev
      --code-changelist 17953583 --data-branch kin-dev --data-changelist 5028487
      --data-directory kindata --region eu --data-clean False  --import-avalanche-state
      --use-linuxclient --licensee BattlefieldGame --email <EMAIL>
      --password "****" --use-recompression-cache --clean-master-version-check
"""

import click
import os
import re
from dice_elipy_scripts.utils import gamescripts_utils
from dice_elipy_scripts.utils.data_build_utils import run_expression_debug_data
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.frosty_build_utils import (
    add_files_to_frosty_output,
    add_frosty_log_to_output,
    fix_data_name_casing,
    generate_buildlayout_xml,
    install_required_sdks,
    patch_eainstaller_signtool,
)
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from elipy2 import (
    avalanche,
    core,
    data,
    filer,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    package,
    running_processes,
    secrets,
    SETTINGS,
    symbols,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbenv_layer, icepick
from elipy2.steam_utils import SteamUtils
from elipy2.telemetry import collect_metrics
from elipy2.oreans import __OUTPUT_SUFFIX as OREANS_OUTPUT_SUFFIX


@click.command("frosty", short_help="Deploys a playable game build with both binaries and data.")
@click.argument("platform")
@click.argument("package_type")
@click.argument("config")
@click.argument("assets", nargs=-1, required=True)
@click.option(
    "--data-directory",
    required=True,
    help="Which data directory to use for the working data set.",
)
@click.option(
    "--code-branch",
    required=True,
    help="Branch/stream to fetch the code/binary build from.",
)
@click.option("--code-changelist", required=True, help="Changelist of binaries to fetch.")
@click.option("--data-branch", help="Branch/stream that data is coming from.", default="")
@click.option("--data-changelist", help="Changelist of data being used.", default="")
@click.option("--region", help="Which region to deploy for (default is ww).", default="ww")
@click.option(
    "--use-deployed-bundles/--not-deployed-bundles",
    help="Used deployed head bundles for frosty build.",
    default=False,
)
@click.option(
    "--local-bundles-path",
    help="Used defined path TnT/local for superbundles.",
    default=None,
)
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for data build.")
@click.option("--frosty-args", multiple=True, help="Frosty arguments.")
@click.option(
    "--use-win64trial/--no-win64trial",
    default=False,
    help="Flag for using win64 trial for win64 patches.",
)
@click.option("--dry-run", is_flag=True, help="Build code without deploying.")
@click.option("--use-oreans", is_flag=True, help="Use oreans binaries.")
@click.option("--use-denuvo", is_flag=True, help="Use denuvo binaries.")
@click.option(
    "--additional-configs",
    multiple=True,
    help="Additional configs to bundle with a loose files build.",
)
@click.option("--import-avalanche-state", is_flag=True, help="Imports Avalanche state from filer.")
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option(
    "--use-recompression-cache",
    is_flag=True,
    help="Alternative Avalanche server to use for the recompression cache",
)
@click.option(
    "--use-linuxclient",
    is_flag=True,
    help="Use linuxclient as the name for linux64 in FrostyIsoTool.",
)
@click.option(
    "--disable-frosty-symbol-upload",
    is_flag=True,
    default=False,
    help="Disable Frosty Symbol Upload.",
)
@click.option(
    "--skip-streaming-install-package",
    is_flag=True,
    default=False,
    help="set STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES to false",
)
@click.option("--trim/--no-trim", default=True)
@click.option("--steam-build", default=False)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@click.option("--enable-eac", is_flag=True, help="Enable EasyAntiCheat")
@click.option(
    "--expression-debug-data",
    is_flag=True,
    help="Export expression debug data after data cook.",
)
@click.option(
    "--keep-intermediate-data",
    is_flag=True,
    help="Keep the intermediate folder after the build completes.",
)
@click.option(
    "--build-gamescripts",
    is_flag=True,
    help="Enable building the gamescripts",
)
@click.option(
    "--run-bespoke",
    is_flag=True,
    help="Run bespoke frosty instead of fbenv frosty",
)
@click.option(
    "--file-hashes",
    default=False,
    help="Create a file with MD5 hashes for all files in the frosty output folder.",
)
@click.option(
    "--use-combine-bundles",
    help="Combine two sets of deployed head bundles into one, and use the result.",
    default=False,
)
@click.option(
    "--combine-code-branch",
    help="Branch for the second set of binaries to use in the the combine workflow.",
)
@click.option(
    "--combine-code-changelist",
    help="Changelist for the second set of binaries to use in the the combine workflow.",
)
@click.option(
    "--combine-data-branch",
    help="Branch for the second set of data to use in the the combine workflow.",
)
@click.option(
    "--combine-data-changelist",
    help="Changelist for the second set of data to use in the the combine workflow.",
)
@click.option(
    "--combine-settings-file",
    type=str,
    default=None,
    help="Settings file used for the combine process",
)
@click.option(
    "--use-precreated-combined-bundles",
    is_flag=True,
    default=False,
    help="Use pre-created combined bundles from network share instead of creating them locally.",
)
@click.option(
    "--fetch-pipeline",
    type=bool,
    default=True,
    help="whether or not to fetch pipeline from filer.",
)
@click.option(
    "--content-layer",
    default=None,
    help="Specific content layer to run a cook on",
)
@click.option(
    "--virtual-branch-override",
    type=bool,
    default=False,
    help="Override the Perforce depot branch with the virtual branch used in the job",
)
@click.option(
    "--clean-steam-sdk",
    default=False,
    type=click.BOOL,
    help="Force re-download and replace Steam SDK on the file system",
)
@click.option(
    "--steam-drmwrap",
    is_flag=True,
    help="Enable Steam DRM wrapper for the build",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    package_type,
    config,
    assets,
    data_directory,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    region,
    use_deployed_bundles,
    local_bundles_path,
    pipeline_args,
    frosty_args,
    use_win64trial,
    dry_run,
    use_oreans,
    use_denuvo,
    additional_configs,
    import_avalanche_state,
    data_clean,
    use_recompression_cache,
    use_linuxclient,
    disable_frosty_symbol_upload,
    skip_streaming_install_package,
    trim,
    steam_build,
    licensee,
    password,
    email,
    domain_user,
    clean_master_version_check,
    enable_eac,
    expression_debug_data,
    keep_intermediate_data,
    build_gamescripts,
    run_bespoke,
    file_hashes,
    use_combine_bundles,
    combine_code_branch,
    combine_code_changelist,
    combine_data_branch,
    combine_data_changelist,
    combine_settings_file,
    use_precreated_combined_bundles,
    fetch_pipeline,
    content_layer,
    virtual_branch_override,
    clean_steam_sdk,
    steam_drmwrap,
):
    # pylint: disable=too-many-statements
    """
    Deploys a playable game build with both binaries and data.

    Examples:
        * elipy --location criterion frosty win64 files final StagingLevels
          --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
          --data-changelist 436418 --data-directory Data --use-deployed-bundles
        * elipy --location criterion frosty win64 files final StagingLevels
          --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
          --data-changelist 436418 --data-directory Data
        * elipy --location criterion frosty win64 files final StagingLevels
          --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
          --data-changelist 436418 --data-directory Data --region ww --data-clean False
          --additional-configs retail --email <EMAIL> --password "****"
          --clean-master-version-check
        * elipy --location dice frosty ps4 digital final ShippingLevels --code-branch kin-dev
          --code-changelist 17953583 --data-branch kin-dev --data-changelist 5028487
          --data-directory kindata --region eu --data-clean False  --import-avalanche-state
          --use-linuxclient --licensee BattlefieldGame --email <EMAIL>
          --password "****" --use-recompression-cache --clean-master-version-check
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    platform = platform.lower()
    package_type = package_type.lower()
    config = config.lower()
    additional_configs = list(additional_configs)
    pipeline_args = list(pipeline_args)
    indexing_args = []
    extra_frosty_args = []

    if virtual_branch_override:
        pipeline_args += ["-BuildSettings.ForceBranch", data_branch]
        # Needed for the cook
        pipeline_args += ["-stateId", data_branch]
        indexing_args += ["-stateId", data_branch]
        # Needed for FrostyISOTool
        extra_frosty_args.append("DATABASE_ID={}".format(os.environ.get("FB_DEFAULT_PLATFORM_DB")))
        extra_frosty_args.append("FB_BRANCH_ID={}".format(data_branch))

    # Clean up before running the job.
    running_processes.kill()
    icepick.IcepickUtils.clean_local_frosty()

    # Initialize
    builder = data.DataUtils(platform, list(assets), monkey_build_label=data_changelist)
    _filer = filer.FilerUtils()

    if content_layer and content_layer.lower() == "source":
        content_layer = None

    # Pre-Check if the dest folder already exist in filer, abort job immediately
    deploy_package_type = "steam" if steam_build else package_type
    if use_combine_bundles:
        deploy_package_type += "_combine"
        dest = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            package_type=deploy_package_type,
            region=region,
            config=config,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
            content_layer=content_layer,
        )
    else:
        dest = filer_paths.get_frosty_build_path(
            data_branch,
            data_changelist,
            code_branch,
            code_changelist,
            platform,
            deploy_package_type,
            region,
            config,
            content_layer=content_layer,
        )
    if os.path.exists(dest) and not dry_run:
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            Possibly because a previous build succeeded in deploying before failing.\
            This can cause us to lose binaries and symbols and is not allowed."
        )

    # Set use-existing-bundles
    use_existing_bundles = use_deployed_bundles or local_bundles_path is not None

    # Set licensee
    set_licensee(list(licensee), list())

    # Set data directory.
    builder.set_datadir(data_directory)

    # Fetch pipeline binary
    if fetch_pipeline:
        _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    if local_bundles_path:
        bundles_location_data = os.path.join(
            frostbite_core.get_tnt_root(), "local", local_bundles_path
        )
    else:
        bundles_location_data = local_paths.get_local_bundles_path()
    bundles_location = os.path.split(bundles_location_data)[0]

    # Patch signtool
    patch_eainstaller_signtool(password=password, user=email, domain_user=domain_user)

    if data_clean.lower() == "true":
        builder.clean()
    # Import previous Avalanche state
    elif import_avalanche_state and not use_existing_bundles and not use_combine_bundles:
        extra_args = import_avalanche_data_state(
            data_branch, code_branch, platform, _filer, data_changelist
        )
        pipeline_args = pipeline_args + extra_args

    if content_layer and (use_existing_bundles or use_combine_bundles):
        raise ELIPYException(
            "Content layer is not supported when using existing bundles.\
            Please remove the content layer argument."
        )

    bundles_platform = platform
    if platform == "win32":
        bundles_platform = "win64"
    elif platform == "linuxserver":
        bundles_platform = "server"
    if use_combine_bundles:
        if use_precreated_combined_bundles:
            # Use pre-created combined bundles from network share
            _filer.fetch_head_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                platform=bundles_platform,
                dest=bundles_location,
                bundles_dir_name="bundles_combine",
            )
        else:
            # Create combined bundles locally (existing behavior)
            # Fetch bundles set 1
            bundles_location_main = os.path.split(
                local_paths.get_local_bundles_path(
                    deployed_bundles_dir_name="deployed_bundles_main"
                )
            )[0]
            _filer.fetch_head_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                platform=bundles_platform,
                dest=bundles_location_main,
                bundles_dir_name="combine_bundles",
            )
            # Fetch bundles set 2
            bundles_location_combine = os.path.split(
                local_paths.get_local_bundles_path(
                    deployed_bundles_dir_name="deployed_bundles_combine"
                )
            )[0]
            _filer.fetch_head_bundles(
                code_branch=combine_code_branch,
                code_changelist=combine_code_changelist,
                data_branch=combine_data_branch,
                data_changelist=combine_data_changelist,
                platform=bundles_platform,
                dest=bundles_location_combine,
                bundles_dir_name="combine_bundles",
            )
            # Combine the two sets of bundles
            extra_combine_args = ["-s"]
            if combine_settings_file:
                extra_combine_args.append(combine_settings_file)
            else:
                if platform == "xbsx":
                    extra_combine_args.append("project-combine-hres-smart-delivery.yaml")
                else:
                    extra_combine_args.append("project-combine-hres.yaml")
            avalanche.combine(
                input_dir_1=bundles_location_main,
                input_dir_2=bundles_location_combine,
                output_dir=bundles_location,
                extra_combine_args=extra_combine_args,
            )
            if not dry_run:
                # Deploy avalanche combine output for safekeeping
                _filer.deploy_avalanche_combine_output(
                    bundles_location,
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                )

        extra_frosty_args += [
            "USE_ALREADYCREATED_SUPERBUNDLES=true",
            "SUPERBUNDLE_SOURCE_DIR={0}".format(bundles_location_data),
        ]
    elif use_existing_bundles and platform not in ["server", "linuxserver", "linux64"]:
        extra_frosty_args += [
            "USE_ALREADYCREATED_SUPERBUNDLES=true",
            "SUPERBUNDLE_SOURCE_DIR={0}".format(bundles_location_data),
        ]

        if use_deployed_bundles:
            _filer.fetch_head_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                platform=bundles_platform,
                dest=bundles_location,
            )
    else:
        use_deployed_bundles = False

        # Build data
        cook_args = {
            "pipeline_args": pipeline_args,
            "indexing_args": indexing_args,
            "collect_mdmps": True,
            "trim": trim,
            "clean_master_version_check": clean_master_version_check,
        }

        if content_layer:
            cook_args["pipeline_args"] += ["-activeContentLayer", content_layer]

        builder.cook(**cook_args)
        avalanche.set_avalanche_build_status(
            code_changelist=code_changelist,
            data_changelist=data_changelist,
            data_branch=data_branch,
            platform=platform,
        )
        if expression_debug_data:
            run_expression_debug_data(
                code_changelist,
                data_changelist,
                code_branch,
                data_branch,
                platform,
                builder_instance=builder,
                clean_master_version_check=clean_master_version_check,
            )

    frosty_platform = platform
    if platform == "linux64" and use_linuxclient:
        # This is a workaround for an issue where FrostyIsoTool on dev-na and related streams
        # doesn't have linux64 as an accepted platform.
        frosty_platform = "linuxclient"
    elif (
        platform == "xb1"
        and frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2)
        and not frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Alpha")
    ):
        # This is a temporary workaround for getting xb1 to work while issues around a switch
        # from XDK to GDK are being solved.
        # This is solved in 2022-1.Alpha, and therefore not needed from that version.
        frosty_platform = "xb1gdk"

    core.update_shell()
    packager = package.PackageUtils(
        frosty_platform,
        package_type,
        config,
        monkey_build_label=data_changelist,
        overwrite_p4config=False,
    )

    install_required_sdks(password=password, user=email, domain_user=domain_user, platform=platform)

    # Fetch game binaries
    code_platform = platform
    if platform in ["win64", "win32"]:
        code_platform = "win64game"
    _filer.fetch_code(code_branch, code_changelist, code_platform, config)
    if use_combine_bundles:
        sp_local_root = os.path.join(local_paths.frostbite_core.get_tnt_root(), "SP_Local")
        # Set a different local root for SP
        fbenv_layer.set_local_root(sp_local_root)
        # Fetch code for SP
        _filer.fetch_code(combine_code_branch, combine_code_changelist, code_platform, config)
        # Set the SP local root as a frosty arg
        extra_frosty_args.append("ADDITIONAL_LOCAL_ROOT={}".format(sp_local_root))
        # Reset the local root
        fbenv_layer.set_local_root(os.path.join(local_paths.frostbite_core.get_tnt_root(), "Local"))

    # Support multiple binaries in the same loose files build
    # (saves a bunch of space for us and sync times for users)
    if additional_configs:
        if package_type != "files":
            raise ELIPYException("Can't create a non loose files build with multiple configs!")
        LOGGER.info("Fetching additional configs: {}".format(" ".join(additional_configs)))
        for _config in additional_configs:
            _filer.fetch_code(code_branch, code_changelist, code_platform, _config)

    if keep_intermediate_data:
        extra_frosty_args += ["CLEAN_INTERMEDIATE_AFTERBUILD=false"]

    if frostbite_core.get_licensee_id().lower() in ["roboto"]:
        extra_frosty_args.append("COBRA_ON_ROBOTO=True")

    if use_recompression_cache is True:
        recompression_cache = SETTINGS.get("recompression_cache")[platform]
        LOGGER.info(
            "Using Alternative Avalanche server for the \
                    recompression cache {}".format(
                recompression_cache
            )
        )
        extra_frosty_args.append("RECOMPRESSION_CACHE_SERVER={0}".format(recompression_cache))

    if enable_eac:
        if (
            platform in ["win64"]
            and package_type in ["files", "digital"]
            and config in ["final", "retail"]
        ):
            extra_frosty_args.append("ANTICHEAT_ENABLED=True")

    if platform in ["xb1", "xbsx"]:
        if not frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
            packager.copy_submissionvalidator()
        if SETTINGS.get("skip_frosty_game_config_flags") != "true":
            extra_frosty_args.append("UPDCOMPAT=3")

    if platform == "xb1":
        if SETTINGS.get("skip_frosty_game_config_flags") != "true":
            if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
                extra_frosty_args.append("GameDK={0}".format(os.environ["GameDK"]))
            else:
                extra_frosty_args.append("DurangoXDK={0}".format(os.environ["DurangoXDK"]))

            if config == "retail":
                extra_frosty_args.append("XBONE_CLEAN_NETWORK_MANIFEST=True")

            if package_type == "files":
                extra_frosty_args.append("XB1_APPXMANIFEST_GENERATE_ALL_CONFIGURATIONS=True")

        extra_frosty_args.append("XB1_PUT_BUILD_LABEL_INTO_VERSION=True")

    if platform == "xbsx":
        extra_frosty_args.append("XBSX_PUT_BUILD_LABEL_INTO_VERSION=True")

    if platform in ["ps4"]:
        if config == "retail" and skip_streaming_install_package is False:
            if SETTINGS.get("skip_frosty_game_config_flags") != "true":
                extra_frosty_args.append("STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES=True")

    if platform in ["win64", "win64game"]:
        if use_win64trial:
            extra_frosty_args.append("IS_TRIAL_BUILD=true")
        else:
            extra_frosty_args.append("IS_TRIAL_BUILD=false")
        if config == "retail" and use_denuvo:
            extra_frosty_args.append("USE_DENUVO=true")
        else:
            extra_frosty_args.append("USE_DENUVO=false")

        # set the oreans executable postfix
        if config == "retail" and use_oreans:
            extra_frosty_args.append("EXECUTABLE_POSTFIX=" + OREANS_OUTPUT_SUFFIX)

        if use_win64trial:
            _filer.fetch_code(code_branch, code_changelist, "win64trial", config, purge=False)
        if steam_build:
            extra_frosty_args.extend(
                [
                    "WIN32_DIGITAL_PLATFORM=Steam",
                    f'STEAM_BUILD_DESCRIPTION="{config} build from {data_branch} '
                    f'on CL {data_changelist}"',
                ]
            )
            if use_combine_bundles:
                steam_sdk_root = SteamUtils.download_steam_sdk(clean_steam_sdk)
                steam_user = SteamUtils.retrieve_steam_credentials(steam_sdk_root)
                extra_frosty_args.extend(
                    [
                        f"STEAM_DRMWRAP={'True' if steam_drmwrap else 'False'}",
                        f"STEAM_USERID={steam_user}",
                    ]
                )

    if build_gamescripts:
        gamescripts_utils.generate_gamescripts()

    # Get secrets needed for packaging
    secrets.remove_old_frosty_cert()
    secrets.get_secrets({"build_type": "frosty", "platform": platform})

    packager.frosty(
        region=region,
        frosty_args=list(frosty_args) + extra_frosty_args,
        run_bespoke=run_bespoke,
    )
    add_frosty_log_to_output()
    add_files_to_frosty_output(platform, package_type, config)

    # Generate xml version of buildlayout file
    generate_buildlayout_xml(local_paths.get_local_frosty_path())

    if additional_configs and package_type == "files":
        LOGGER.info("Copying additional configs: {}".format(" ".join(additional_configs)))
        for _config in additional_configs:
            source = local_paths.get_local_build_path(code_platform, _config)
            dest = local_paths.get_local_frosty_path()
            files_to_copy = []
            for file_name in os.listdir(source):
                if re.match(
                    r"^(.*\.Main_.*_.*\.pdb|.*\.Main_[^_]*_[^_]*_[^_|\.]*|\.Main_.*_.*\..*|.*\.exe|.*\.elf|MLEngine-onnxruntime.dll|onnxruntime.dll|Engine.BuildInfo.*|Extension.Twinkle.*\.dll|External.ChakraCore.dll|.*BuildSettings|Engine.Render.Core2.*\.dll|Engine.Render.Core2.*\.prx)$",  # pylint: disable=line-too-long
                    file_name,
                ):
                    if not os.path.isfile(os.path.join(dest, file_name)):
                        files_to_copy.append(file_name)
            if not files_to_copy:
                LOGGER.warning("Can't find files for additional config {}".format(_config))
                # raise ELIPYException("Can't find files for additional config {}".format(_config))
            else:
                core.robocopy(source, dest, extra_args=files_to_copy)
                # Copy additional binaries to the SP (single player) folder, if it exists.
                dest_sp = os.path.join(dest, "SP")
                if os.path.exists(dest_sp):
                    core.robocopy(source, dest_sp, extra_args=files_to_copy)

    if use_existing_bundles and platform not in ["server", "linuxserver", "linux64"]:
        if package_type == "files":
            # When using combine_bundles, skip this step as bundles are already combined
            # builtLevels.json will be generated in the else branch below
            if not use_combine_bundles:
                core.robocopy(
                    source=bundles_location_data,
                    dest=local_paths.get_local_frosty_path(),
                    extra_args=["builtLevels.json", "/s"],
                )
    else:
        if package_type == "files" or platform in ["server", "linuxserver", "linux64"]:
            avalanche.get_built_levels(
                avalanche.get_full_database_name(platform),
                to_file=os.path.join(local_paths.get_local_frosty_path(), "builtLevels.json"),
            )

    # Create a file with MD5 hashes for all files in the frosty output folder.
    if file_hashes and package_type == "files":
        skipped_files = SETTINGS.get("shift_md5_skipped_files", default=[])
        core.md5_hash_folder(
            local_paths.get_local_frosty_path(),
            skipped_files=skipped_files,
            output_file_type="csv",
            chunk_size=65536,
        )

    if not dry_run:
        _filer.create_combine_stream_info_file(
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
        )
        # Deploy packaged build
        fix_data_name_casing()
        if use_combine_bundles:
            _filer.deploy_frosty_build(
                data_changelist=data_changelist,
                data_branch=data_branch,
                code_changelist=code_changelist,
                code_branch=code_branch,
                package_type=deploy_package_type,
                region=region,
                config=config,
                additional_configs=additional_configs,
                dataset=data_directory,
                platform=platform,
                combine_code_changelist=combine_code_changelist,
                combine_code_branch=combine_code_branch,
                combine_data_changelist=combine_data_changelist,
                combine_data_branch=combine_data_branch,
            )
        else:
            _filer.deploy_frosty_build(
                data_changelist=data_changelist,
                data_branch=data_branch,
                code_changelist=code_changelist,
                code_branch=code_branch,
                package_type=deploy_package_type,
                region=region,
                config=config,
                additional_configs=additional_configs,
                dataset=data_directory,
                platform=platform,
                content_layer=content_layer,
            )

        # Upload renamed binary to symstore
        if "win64" in platform and package_type == "digital" and not disable_frosty_symbol_upload:
            _symbols = symbols.SymbolsUtils()
            _symbols.upload_game_binary(
                platform=platform,
                config=config,
                package_type=deploy_package_type,
                data_changelist=data_changelist,
                code_changelist=code_changelist,
                data_branch=data_branch,
                code_branch=code_branch,
            )
            if use_win64trial:
                _symbols.upload_game_binary(
                    platform="win64trial",
                    config=config,
                    package_type="digital",
                    data_changelist=data_changelist,
                    code_changelist=code_changelist,
                    data_branch=data_branch,
                    code_branch=code_branch,
                )
